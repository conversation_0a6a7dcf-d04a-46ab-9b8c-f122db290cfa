// This is a comprehensive example demonstrating the Generic CRUD System
// Run with: cargo run --example generic_crud_demo

use platform_rust::{
    errors::Result,
    repository::{
        base_repository::{CrudRepository, Entity},
        generic_pagination::{PaginatedRepository, PaginatedResponse, GenericPaginationRequest},
    },
    services::generic_service::{GenericCrudService, UniqueFieldValidator},
    utils::{<PERSON>rror<PERSON>elper, pagination::PaginationRequest},
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use uuid::Uuid;
use validator::Validate;

// ===== EXAMPLE ENTITY =====

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Product {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub price: f64,
    pub is_active: bool,
}

impl Entity for Product {
    type Id = Uuid;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

// ===== REQUEST/RESPONSE MODELS =====

#[derive(Debug, Deserialize, Validate)]
pub struct CreateProductRequest {
    #[validate(length(min = 1, message = "Name is required"))]
    pub name: String,
    pub description: Option<String>,
    #[validate(range(min = 0.0, message = "Price must be positive"))]
    pub price: f64,
}



#[derive(Debug, Deserialize, Validate)]
pub struct UpdateProductRequest {
    #[validate(length(min = 1, message = "Name cannot be empty"))]
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    #[validate(range(min = 0.0, message = "Price must be positive"))]
    pub price: Option<f64>,
    pub is_active: Option<bool>,
}



// ===== MOCK REPOSITORY =====

pub struct MockProductRepository {
    products: std::sync::Mutex<HashMap<Uuid, Product>>,
}

impl MockProductRepository {
    pub fn new() -> Self {
        Self {
            products: std::sync::Mutex::new(HashMap::new()),
        }
    }
}

#[async_trait]
impl CrudRepository<Product> for MockProductRepository {
    type CreateParams = CreateProductRequest;
    type UpdateParams = UpdateProductRequest;
    type Filter = String;

    async fn create(&self, params: Self::CreateParams) -> Result<Product> {
        let product = Product {
            id: Uuid::new_v4(),
            name: params.name,
            description: params.description,
            price: params.price,
            is_active: true,
        };

        let mut products = self.products.lock().unwrap();
        products.insert(product.id, product.clone());
        Ok(product)
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Product>> {
        let products = self.products.lock().unwrap();
        Ok(products.get(id).cloned())
    }

    async fn update(&self, id: &Uuid, params: Self::UpdateParams) -> Result<Product> {
        let mut products = self.products.lock().unwrap();
        let product = products.get_mut(id)
            .ok_or_else(|| ErrorHelper::not_found("Product", Some(&id.to_string())))?;

        if let Some(name) = params.name {
            product.name = name;
        }
        if let Some(description) = params.description {
            product.description = description;
        }
        if let Some(price) = params.price {
            product.price = price;
        }
        if let Some(is_active) = params.is_active {
            product.is_active = is_active;
        }

        Ok(product.clone())
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let mut products = self.products.lock().unwrap();
        products.remove(id)
            .ok_or_else(|| ErrorHelper::not_found("Product", Some(&id.to_string())))?;
        Ok(())
    }

    async fn find_all(&self, _filter: Option<Self::Filter>) -> Result<Vec<Product>> {
        let products = self.products.lock().unwrap();
        Ok(products.values().cloned().collect())
    }
}

#[async_trait]
impl PaginatedRepository<Product> for MockProductRepository {
    type PaginationRequest = GenericPaginationRequest;

    async fn get_paginated(&self, request: Self::PaginationRequest) -> Result<PaginatedResponse<Product>> {
        let products = self.products.lock().unwrap();
        let all_products: Vec<Product> = products.values().cloned().collect();
        
        let total = all_products.len() as i64;
        let offset = request.offset() as usize;
        let limit = request.limit() as usize;
        
        let data = all_products
            .into_iter()
            .skip(offset)
            .take(limit)
            .collect();

        Ok(PaginatedResponse::new(data, request.page(), request.limit(), total))
    }
}

// ===== GENERIC SERVICE IMPLEMENTATION =====

pub struct ProductService {
    crud_service: GenericCrudService<Product, MockProductRepository>,
    repository: Arc<MockProductRepository>,
}

impl ProductService {
    pub fn new(repository: Arc<MockProductRepository>) -> Self {
        Self {
            crud_service: GenericCrudService::new(repository.clone(), "Product"),
            repository,
        }
    }

    pub async fn create_product(&self, request: CreateProductRequest) -> Result<Product> {
        // Add custom validation if needed
        self.validate_field_unique("name", &request.name, None, "Product").await?;
        
        // Use generic service
        self.crud_service.create_with_validation(request).await
    }

    pub async fn get_product_by_id(&self, id: &Uuid) -> Result<Product> {
        self.crud_service.get_by_id_or_error(id).await
    }

    pub async fn update_product(&self, id: &Uuid, request: UpdateProductRequest) -> Result<Product> {
        if let Some(ref name) = request.name {
            self.validate_field_unique("name", name, Some(id), "Product").await?;
        }
        
        self.crud_service.update_with_validation(id, request).await
    }

    pub async fn delete_product(&self, id: &Uuid) -> Result<()> {
        self.crud_service.delete_with_validation(id).await
    }

    pub async fn get_products_paginated(&self, request: GenericPaginationRequest) -> Result<PaginatedResponse<Product>> {
        self.crud_service.get_paginated(request).await
    }
}

#[async_trait]
impl UniqueFieldValidator<Product> for ProductService {
    async fn is_field_unique(&self, field_name: &str, field_value: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        match field_name {
            "name" => {
                let products = self.repository.products.lock().unwrap();
                let exists = products.values().any(|p| {
                    p.name == field_value && exclude_id.map_or(true, |id| &p.id != id)
                });
                Ok(!exists)
            }
            _ => Ok(true),
        }
    }
}

// ===== DEMO FUNCTION =====

#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 Generic CRUD System Demo");
    println!("============================");

    // Create repository and service
    let repository = Arc::new(MockProductRepository::new());
    let service = ProductService::new(repository);

    // Demo 1: Create products
    println!("\n📝 Creating products...");
    let product1 = service.create_product(CreateProductRequest {
        name: "Laptop".to_string(),
        description: Some("High-performance laptop".to_string()),
        price: 999.99,
    }).await?;
    println!("✅ Created: {} (ID: {})", product1.name, product1.id);

    let product2 = service.create_product(CreateProductRequest {
        name: "Mouse".to_string(),
        description: Some("Wireless mouse".to_string()),
        price: 29.99,
    }).await?;
    println!("✅ Created: {} (ID: {})", product2.name, product2.id);

    // Demo 2: Get product by ID
    println!("\n🔍 Getting product by ID...");
    let retrieved = service.get_product_by_id(&product1.id).await?;
    println!("✅ Retrieved: {} - ${}", retrieved.name, retrieved.price);

    // Demo 3: Update product
    println!("\n✏️ Updating product...");
    let updated = service.update_product(&product1.id, UpdateProductRequest {
        name: None,
        description: Some(Some("Updated description".to_string())),
        price: Some(899.99),
        is_active: None,
    }).await?;
    println!("✅ Updated: {} - ${}", updated.name, updated.price);

    // Demo 4: Get paginated products
    println!("\n📄 Getting paginated products...");
    let paginated = service.get_products_paginated(GenericPaginationRequest {
        page: 1,
        limit: 10,
        search: None,
        is_active: None,
    }).await?;
    println!("✅ Found {} products (page {}/{})", 
        paginated.data.len(), 
        paginated.pagination.page, 
        paginated.pagination.total_pages
    );

    // Demo 5: Delete product
    println!("\n🗑️ Deleting product...");
    service.delete_product(&product2.id).await?;
    println!("✅ Deleted product: {}", product2.id);

    // Demo 6: Try to get deleted product (should fail)
    println!("\n❌ Trying to get deleted product...");
    match service.get_product_by_id(&product2.id).await {
        Ok(_) => println!("❌ Unexpected: Product still exists"),
        Err(e) => println!("✅ Expected error: {}", e),
    }

    println!("\n🎉 Demo completed successfully!");
    println!("\n📊 Benefits demonstrated:");
    println!("  • Generic CRUD operations");
    println!("  • Automatic validation");
    println!("  • Consistent error handling");
    println!("  • Pagination support");
    println!("  • Type safety");
    println!("  • Code reusability");

    Ok(())
}
