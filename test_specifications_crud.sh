#!/bin/bash

# Test script for Specifications CRUD endpoints
# Usage: ./test_specifications_crud.sh

BASE_URL="http://localhost:8386/api"
CONTENT_TYPE="Content-Type: application/json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Specifications CRUD Endpoints${NC}"
echo "=========================================="

# Step 1: Login to get JWT token
echo -e "\n${YELLOW}Step 1: Login to get JWT token${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "$CONTENT_TYPE" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin123!"
  }')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.access_token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo -e "${RED}❌ Failed to get access token${NC}"
  echo "Response: $LOGIN_RESPONSE"
  exit 1
fi
echo -e "${GREEN}✅ Got access token${NC}"

AUTH_HEADER="Authorization: Bearer $TOKEN"

# Step 2: Create a category first
echo -e "\n${YELLOW}Step 2: Create/Get category for testing${NC}"
CATEGORIES_RESPONSE=$(curl -s -X GET "$BASE_URL/categories/type/laptops" \
  -H "$AUTH_HEADER")
CATEGORY_ID=$(echo $CATEGORIES_RESPONSE | jq -r '.data[0].id')
echo -e "${GREEN}✅ Using category ID: $CATEGORY_ID${NC}"

# Step 3: Create a laptop
echo -e "\n${YELLOW}Step 3: Create a laptop for specifications testing${NC}"
TIMESTAMP=$(date +%s)
LAPTOP_RESPONSE=$(curl -s -X POST "$BASE_URL/laptops" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "brand": "TestBrand",
    "model": "Spec Test Pro",
    "full_name": "TestBrand Spec Test Pro Laptop",
    "slug": "test-spec-laptop-crud-'$TIMESTAMP'",
    "category_id": "'$CATEGORY_ID'",
    "sku": "TSL-SPEC-'$TIMESTAMP'",
    "description": "Test laptop for specifications CRUD testing",
    "is_featured": false
  }')

LAPTOP_ID=$(echo $LAPTOP_RESPONSE | jq -r '.data.id')
if [ "$LAPTOP_ID" = "null" ] || [ -z "$LAPTOP_ID" ]; then
  echo -e "${RED}❌ Failed to create laptop${NC}"
  echo "Response: $LAPTOP_RESPONSE"
  exit 1
fi
echo -e "${GREEN}✅ Created laptop with ID: $LAPTOP_ID${NC}"

# Step 4: Test Specifications CRUD endpoints
echo -e "\n${YELLOW}Step 4: Testing Specifications CRUD endpoints${NC}"

# Test 1: Create Specification
echo -e "\n${BLUE}Test 1: Create Specification${NC}"
SPEC_RESPONSE=$(curl -s -X POST "$BASE_URL/specifications" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "laptop_id": "'$LAPTOP_ID'",
    "cpu_brand": "Intel",
    "cpu_model": "Core i7-12700H",
    "ram_size": 16,
    "ram_type": "DDR4",
    "ram_soldered": false,
    "storage_type": "SSD",
    "storage_capacity": 512,
    "gpu_type": "Dedicated",
    "gpu_model": "RTX 3070",
    "screen_size": 15.6,
    "screen_resolution": "FHD",
    "refresh_rate": 144,
    "weight": 2.3,
    "operating_system": "Windows 11 Home"
  }')

SPEC_ID=$(echo $SPEC_RESPONSE | jq -r '.data.id')
if [ "$SPEC_ID" = "null" ] || [ -z "$SPEC_ID" ]; then
  echo -e "${RED}❌ Failed to create specification${NC}"
  echo "Response: $SPEC_RESPONSE"
else
  echo -e "${GREEN}✅ Created specification with ID: $SPEC_ID${NC}"
fi

# Test 2: Get Specification by ID
echo -e "\n${BLUE}Test 2: Get Specification by ID${NC}"
GET_SPEC_RESPONSE=$(curl -s -X GET "$BASE_URL/specifications/$SPEC_ID" \
  -H "$AUTH_HEADER")

RETRIEVED_SPEC_ID=$(echo $GET_SPEC_RESPONSE | jq -r '.data.id')
if [ "$RETRIEVED_SPEC_ID" = "$SPEC_ID" ]; then
  echo -e "${GREEN}✅ Successfully retrieved specification${NC}"
else
  echo -e "${RED}❌ Failed to retrieve specification${NC}"
  echo "Response: $GET_SPEC_RESPONSE"
fi

# Test 3: Get Specifications by Laptop ID (Public API)
echo -e "\n${BLUE}Test 3: Get Specifications by Laptop ID (Public API)${NC}"
LAPTOP_SPECS_RESPONSE=$(curl -s -X GET "$BASE_URL/laptops/$LAPTOP_ID/specifications")

LAPTOP_SPEC_ID=$(echo $LAPTOP_SPECS_RESPONSE | jq -r '.data.id')
if [ "$LAPTOP_SPEC_ID" = "$SPEC_ID" ]; then
  echo -e "${GREEN}✅ Successfully retrieved specification for laptop${NC}"
else
  echo -e "${RED}❌ Failed to retrieve specification for laptop${NC}"
  echo "Response: $LAPTOP_SPECS_RESPONSE"
fi

# Test 4: Update Specification
echo -e "\n${BLUE}Test 4: Update Specification${NC}"
UPDATE_SPEC_RESPONSE=$(curl -s -X PUT "$BASE_URL/specifications/$SPEC_ID" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "cpu_model": "Core i7-13700H",
    "ram_size": 32,
    "storage_capacity": 1024,
    "refresh_rate": 165
  }')

UPDATED_CPU_MODEL=$(echo $UPDATE_SPEC_RESPONSE | jq -r '.data.cpu_model')
UPDATED_RAM_SIZE=$(echo $UPDATE_SPEC_RESPONSE | jq -r '.data.ram_size')
UPDATED_REFRESH_RATE=$(echo $UPDATE_SPEC_RESPONSE | jq -r '.data.refresh_rate')
if [ "$UPDATED_CPU_MODEL" = "Core i7-13700H" ] && [ "$UPDATED_RAM_SIZE" = "32" ] && [ "$UPDATED_REFRESH_RATE" = "165" ]; then
  echo -e "${GREEN}✅ Successfully updated specification${NC}"
else
  echo -e "${RED}❌ Failed to update specification${NC}"
  echo "Response: $UPDATE_SPEC_RESPONSE"
fi

# Test 5: Verify updated specification
echo -e "\n${BLUE}Test 5: Verify updated specification${NC}"
VERIFY_SPEC_RESPONSE=$(curl -s -X GET "$BASE_URL/specifications/$SPEC_ID" \
  -H "$AUTH_HEADER")

VERIFIED_CPU_MODEL=$(echo $VERIFY_SPEC_RESPONSE | jq -r '.data.cpu_model')
VERIFIED_RAM_SIZE=$(echo $VERIFY_SPEC_RESPONSE | jq -r '.data.ram_size')
VERIFIED_REFRESH_RATE=$(echo $VERIFY_SPEC_RESPONSE | jq -r '.data.refresh_rate')
if [ "$VERIFIED_CPU_MODEL" = "Core i7-13700H" ] && [ "$VERIFIED_RAM_SIZE" = "32" ] && [ "$VERIFIED_REFRESH_RATE" = "165" ]; then
  echo -e "${GREEN}✅ Specification update verified successfully${NC}"
else
  echo -e "${RED}❌ Specification update verification failed${NC}"
  echo "Response: $VERIFY_SPEC_RESPONSE"
fi

# Test 6: Delete Specification
echo -e "\n${BLUE}Test 6: Delete Specification${NC}"
DELETE_SPEC_RESPONSE=$(curl -s -X DELETE "$BASE_URL/specifications/$SPEC_ID" \
  -H "$AUTH_HEADER")

DELETE_SUCCESS=$(echo $DELETE_SPEC_RESPONSE | jq -r '.data.deleted')
if [ "$DELETE_SUCCESS" = "true" ]; then
  echo -e "${GREEN}✅ Successfully deleted specification${NC}"
else
  echo -e "${RED}❌ Failed to delete specification${NC}"
  echo "Response: $DELETE_SPEC_RESPONSE"
fi

# Test 7: Verify specification is deleted
echo -e "\n${BLUE}Test 7: Verify specification is deleted${NC}"
VERIFY_DELETE_RESPONSE=$(curl -s -X GET "$BASE_URL/specifications/$SPEC_ID" \
  -H "$AUTH_HEADER")

VERIFY_DELETE_STATUS=$(echo $VERIFY_DELETE_RESPONSE | jq -r '.status')
if [ "$VERIFY_DELETE_STATUS" = "404" ]; then
  echo -e "${GREEN}✅ Specification deletion verified (404 Not Found)${NC}"
else
  echo -e "${RED}❌ Specification deletion verification failed${NC}"
  echo "Response: $VERIFY_DELETE_RESPONSE"
fi

# Test 8: Verify no specification for laptop after deletion
echo -e "\n${BLUE}Test 8: Verify no specification for laptop after deletion${NC}"
sleep 1  # Avoid rate limiting
NO_SPEC_RESPONSE=$(curl -s -X GET "$BASE_URL/laptops/$LAPTOP_ID/specifications")

NO_SPEC_DATA=$(echo $NO_SPEC_RESPONSE | jq -r '.data')
if [ "$NO_SPEC_DATA" = "null" ]; then
  echo -e "${GREEN}✅ Confirmed no specification exists for laptop${NC}"
else
  echo -e "${RED}❌ Specification still exists for laptop${NC}"
  echo "Response: $NO_SPEC_RESPONSE"
fi

# Cleanup: Delete laptop
echo -e "\n${YELLOW}Cleanup: Deleting test laptop${NC}"
curl -s -X DELETE "$BASE_URL/laptops/$LAPTOP_ID" \
  -H "$AUTH_HEADER" > /dev/null

echo -e "\n${GREEN}🎉 Specifications CRUD testing completed!${NC}"
echo "=========================================="
