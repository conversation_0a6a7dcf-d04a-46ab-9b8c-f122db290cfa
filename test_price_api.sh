#!/bin/bash

# Test script for Price API endpoints
# Usage: ./test_price_api.sh

BASE_URL="http://localhost:8386/api"
CONTENT_TYPE="Content-Type: application/json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Price API Endpoints${NC}"
echo "=================================="

# Step 1: Login to get JWT token
echo -e "\n${YELLOW}Step 1: Login to get JWT token${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "$CONTENT_TYPE" \
  -d '{
    "email": "<EMAIL>",
    "password": "SuperAdmin123!"
  }')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.access_token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo -e "${RED}❌ Failed to get access token${NC}"
  echo "Response: $LOGIN_RESPONSE"
  exit 1
fi
echo -e "${GREEN}✅ Got access token${NC}"

AUTH_HEADER="Authorization: Bearer $TOKEN"

# Step 2: Create a category first
echo -e "\n${YELLOW}Step 2: Create a category for testing${NC}"
CATEGORY_RESPONSE=$(curl -s -X POST "$BASE_URL/categories" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "name": "Test Laptops",
    "slug": "test-laptops",
    "description": "Test category for laptops",
    "category_type": "laptops"
  }')

CATEGORY_ID=$(echo $CATEGORY_RESPONSE | jq -r '.data.id')
if [ "$CATEGORY_ID" = "null" ] || [ -z "$CATEGORY_ID" ]; then
  echo -e "${YELLOW}⚠️ Category might already exist, trying to get existing one${NC}"
  # Try to get existing category
  CATEGORIES_RESPONSE=$(curl -s -X GET "$BASE_URL/categories/type/laptops" \
    -H "$AUTH_HEADER")
  CATEGORY_ID=$(echo $CATEGORIES_RESPONSE | jq -r '.data[0].id')
fi
echo -e "${GREEN}✅ Using category ID: $CATEGORY_ID${NC}"

# Step 3: Create a laptop
echo -e "\n${YELLOW}Step 3: Create a laptop for price testing${NC}"
LAPTOP_RESPONSE=$(curl -s -X POST "$BASE_URL/laptops" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "brand": "TestBrand",
    "model": "Gaming Pro",
    "full_name": "TestBrand Gaming Pro Test Laptop",
    "slug": "test-gaming-laptop-price",
    "category_id": "'$CATEGORY_ID'",
    "sku": "TGL-PRICE-001",
    "description": "Test laptop for price API testing",
    "is_featured": false
  }')

LAPTOP_ID=$(echo $LAPTOP_RESPONSE | jq -r '.data.id')
if [ "$LAPTOP_ID" = "null" ] || [ -z "$LAPTOP_ID" ]; then
  echo -e "${RED}❌ Failed to create laptop${NC}"
  echo "Response: $LAPTOP_RESPONSE"
  exit 1
fi
echo -e "${GREEN}✅ Created laptop with ID: $LAPTOP_ID${NC}"

# Step 4: Test Price API endpoints
echo -e "\n${YELLOW}Step 4: Testing Price API endpoints${NC}"

# Test 1: Create Price
echo -e "\n${BLUE}Test 1: Create Price${NC}"
PRICE_RESPONSE=$(curl -s -X POST "$BASE_URL/prices" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "laptop_id": "'$LAPTOP_ID'",
    "min_price": 15000000,
    "max_price": 18000000,
    "currency": "VND",
    "source": "FPT Shop",
    "region": "Vietnam",
    "is_current": true
  }')

PRICE_ID=$(echo $PRICE_RESPONSE | jq -r '.data.id')
if [ "$PRICE_ID" = "null" ] || [ -z "$PRICE_ID" ]; then
  echo -e "${RED}❌ Failed to create price${NC}"
  echo "Response: $PRICE_RESPONSE"
else
  echo -e "${GREEN}✅ Created price with ID: $PRICE_ID${NC}"
fi

# Test 2: Get Price by ID
echo -e "\n${BLUE}Test 2: Get Price by ID${NC}"
GET_PRICE_RESPONSE=$(curl -s -X GET "$BASE_URL/prices/$PRICE_ID" \
  -H "$AUTH_HEADER")

RETRIEVED_PRICE_ID=$(echo $GET_PRICE_RESPONSE | jq -r '.data.id')
if [ "$RETRIEVED_PRICE_ID" = "$PRICE_ID" ]; then
  echo -e "${GREEN}✅ Successfully retrieved price${NC}"
else
  echo -e "${RED}❌ Failed to retrieve price${NC}"
  echo "Response: $GET_PRICE_RESPONSE"
fi

# Test 3: Get Prices by Laptop ID (Public API)
echo -e "\n${BLUE}Test 3: Get Prices by Laptop ID (Public API)${NC}"
LAPTOP_PRICES_RESPONSE=$(curl -s -X GET "$BASE_URL/laptops/$LAPTOP_ID/prices")

PRICES_COUNT=$(echo $LAPTOP_PRICES_RESPONSE | jq '.data | length')
if [ "$PRICES_COUNT" -gt 0 ]; then
  echo -e "${GREEN}✅ Successfully retrieved $PRICES_COUNT price(s) for laptop${NC}"
else
  echo -e "${RED}❌ Failed to retrieve prices for laptop${NC}"
  echo "Response: $LAPTOP_PRICES_RESPONSE"
fi

# Test 4: Update Price
echo -e "\n${BLUE}Test 4: Update Price${NC}"
UPDATE_PRICE_RESPONSE=$(curl -s -X PUT "$BASE_URL/prices/$PRICE_ID" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "min_price": 14000000,
    "max_price": 17000000,
    "source": "Thế Giới Di Động"
  }')

UPDATED_MIN_PRICE=$(echo $UPDATE_PRICE_RESPONSE | jq -r '.data.min_price')
if [ "$UPDATED_MIN_PRICE" = "14000000" ]; then
  echo -e "${GREEN}✅ Successfully updated price${NC}"
else
  echo -e "${RED}❌ Failed to update price${NC}"
  echo "Response: $UPDATE_PRICE_RESPONSE"
fi

# Test 5: Create another price for set current test
echo -e "\n${BLUE}Test 5: Create second price for set current test${NC}"
PRICE2_RESPONSE=$(curl -s -X POST "$BASE_URL/prices" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{
    "laptop_id": "'$LAPTOP_ID'",
    "min_price": 13500000,
    "max_price": 16500000,
    "currency": "VND",
    "source": "Shopee",
    "region": "Vietnam",
    "is_current": false
  }')

PRICE2_ID=$(echo $PRICE2_RESPONSE | jq -r '.data.id')
if [ "$PRICE2_ID" = "null" ] || [ -z "$PRICE2_ID" ]; then
  echo -e "${RED}❌ Failed to create second price${NC}"
else
  echo -e "${GREEN}✅ Created second price with ID: $PRICE2_ID${NC}"
fi

# Test 6: Set Current Price
echo -e "\n${BLUE}Test 6: Set Current Price${NC}"
SET_CURRENT_RESPONSE=$(curl -s -X PUT "$BASE_URL/laptops/$LAPTOP_ID/prices/$PRICE2_ID/set-current" \
  -H "$AUTH_HEADER")

SET_CURRENT_SUCCESS=$(echo $SET_CURRENT_RESPONSE | jq -r '.data.set_as_current')
if [ "$SET_CURRENT_SUCCESS" = "true" ]; then
  echo -e "${GREEN}✅ Successfully set current price${NC}"
else
  echo -e "${RED}❌ Failed to set current price${NC}"
  echo "Response: $SET_CURRENT_RESPONSE"
fi

# Test 7: Get Current Prices Only
echo -e "\n${BLUE}Test 7: Get Current Prices Only${NC}"
CURRENT_PRICES_RESPONSE=$(curl -s -X GET "$BASE_URL/laptops/$LAPTOP_ID/prices?current_only=true")

CURRENT_PRICES_COUNT=$(echo $CURRENT_PRICES_RESPONSE | jq '.data | length')
echo -e "${GREEN}✅ Retrieved $CURRENT_PRICES_COUNT current price(s)${NC}"

# Test 8: Delete Price
echo -e "\n${BLUE}Test 8: Delete Price${NC}"
DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/prices/$PRICE_ID" \
  -H "$AUTH_HEADER")

DELETE_SUCCESS=$(echo $DELETE_RESPONSE | jq -r '.success')
if [ "$DELETE_SUCCESS" = "true" ]; then
  echo -e "${GREEN}✅ Successfully deleted price${NC}"
else
  echo -e "${RED}❌ Failed to delete price${NC}"
  echo "Response: $DELETE_RESPONSE"
fi

# Cleanup: Delete laptop
echo -e "\n${YELLOW}Cleanup: Deleting test laptop${NC}"
curl -s -X DELETE "$BASE_URL/laptops/$LAPTOP_ID" \
  -H "$AUTH_HEADER" > /dev/null

echo -e "\n${GREEN}🎉 Price API testing completed!${NC}"
echo "=================================="
