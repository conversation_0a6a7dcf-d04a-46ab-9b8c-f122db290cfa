{"info": {"_postman_id": "categories-laptops-collection", "name": "Platform Rust - Categories & Laptops", "description": "Categories, Laptops and Specifications Management APIs for Platform Rust system.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "📦 Category Management", "item": [{"name": "Create Category", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('created_category_id', response.data.id);", "        console.log('✅ Category created with ID: ' + response.data.id);", "    }", "}", "pm.test('Category created successfully', function () { pm.response.to.have.status(201); });"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Gaming Laptops\",\n    \"description\": \"High-performance laptops for gaming\",\n    \"category_type\": \"laptop\"\n}"}, "url": {"raw": "{{base_url}}/api/categories", "host": ["{{base_url}}"], "path": ["api", "categories"]}}, "response": []}, {"name": "Get Category by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Category retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains category data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "    pm.expect(response.data).to.have.property('description');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/{{created_category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{created_category_id}}"]}}, "response": []}, {"name": "Get Categories with Pagination", "event": [{"listen": "test", "script": {"exec": ["pm.test('Categories list retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains pagination info', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('data');", "    pm.expect(response.data).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "categories"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Update Category", "event": [{"listen": "test", "script": {"exec": ["pm.test('Category updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated category data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Gaming Laptops (Updated)\",\n    \"description\": \"An updated description for high-performance laptops.\",\n    \"category_type\": \"laptop\"\n}"}, "url": {"raw": "{{base_url}}/api/categories/{{created_category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{created_category_id}}"]}}, "response": []}, {"name": "Delete Category", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/{{created_category_id}}", "host": ["{{base_url}}"], "path": ["api", "categories", "{{created_category_id}}"]}}, "response": []}, {"name": "Get Categories by Type", "event": [{"listen": "test", "script": {"exec": ["pm.test('Categories by type retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains categories array', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/type/laptop", "host": ["{{base_url}}"], "path": ["api", "categories", "type", "laptop"]}}, "response": []}, {"name": "Toggle Category Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Category status toggled successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated category', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('is_active');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/categories/{{created_category_id}}/toggle-status", "host": ["{{base_url}}"], "path": ["api", "categories", "{{created_category_id}}", "toggle-status"]}}, "response": []}]}, {"name": "💻 Laptop Management", "item": [{"name": "🔓 Get Laptops (Public API)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Public laptops retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains pagination info', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('data');", "    pm.expect(response.data).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops?page=1&limit=10&status=published", "host": ["{{base_url}}"], "path": ["api", "laptops"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "published"}]}}, "response": []}, {"name": "🔓 Get Laptop by Slug (Public API)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Laptop retrieved by slug successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains laptop data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('slug');", "    pm.expect(response.data).to.have.property('brand');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/laptops/by-slug/legion-pro-7i", "host": ["{{base_url}}"], "path": ["api", "laptops", "by-slug", "legion-pro-7i"]}}, "response": []}, {"name": "🔓 Increment Laptop View Count (Public API)", "event": [{"listen": "test", "script": {"exec": ["pm.test('View count incremented successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms view increment', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('view_incremented');", "    pm.expect(response.data.view_incremented).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}/view", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}", "view"]}}, "response": []}, {"name": "Create Lapt<PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('created_laptop_id', response.data.id);", "        console.log('✅ Laptop created with ID: ' + response.data.id);", "    }", "}", "pm.test('<PERSON>pt<PERSON> created successfully', function () { pm.response.to.have.status(201); });"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Lenovo\",\n    \"model\": \"Legion Pro 7i\",\n    \"full_name\": \"Lenovo Legion Pro 7i Gen 9\",\n    \"market_region\": \"VN\",\n    \"category_id\": \"{{created_category_id}}\",\n    \"image_urls\": [\"https://example.com/laptop1.jpg\"]\n}"}, "url": {"raw": "{{base_url}}/api/laptops", "host": ["{{base_url}}"], "path": ["api", "laptops"]}}, "response": []}, {"name": "Get Laptop by ID (Private API)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Lapt<PERSON> retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains full laptop data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('brand');", "    pm.expect(response.data).to.have.property('model');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}"]}}, "response": []}, {"name": "Get Laptops with Pagination (Private API)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Laptops list retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains pagination info', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('data');", "    pm.expect(response.data).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "laptops"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Update Laptop", "event": [{"listen": "test", "script": {"exec": ["pm.test('<PERSON><PERSON><PERSON> updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated laptop data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('brand');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"brand\": \"Lenovo\",\n    \"model\": \"Legion Pro 7i Gen 9\",\n    \"full_name\": \"Lenovo Legion Pro 7i Gen 9 (Updated)\",\n    \"market_region\": \"VN\"\n}"}, "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}"]}}, "response": []}, {"name": "Publish Laptop", "event": [{"listen": "test", "script": {"exec": ["pm.test('<PERSON><PERSON><PERSON> published successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms publication', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('status');", "    pm.expect(response.data.status).to.equal('published');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}/publish", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}", "publish"]}}, "response": []}, {"name": "Archive Laptop", "event": [{"listen": "test", "script": {"exec": ["pm.test('Laptop archived successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms archival', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('status');", "    pm.expect(response.data.status).to.equal('archived');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}/archive", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}", "archive"]}}, "response": []}, {"name": "Set Laptop Featured", "event": [{"listen": "test", "script": {"exec": ["pm.test('Laptop featured status updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms featured status', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('is_featured');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"is_featured\": true\n}"}, "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}/featured", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}", "featured"]}}, "response": []}, {"name": "Delete Laptop", "event": [{"listen": "test", "script": {"exec": ["pm.test('<PERSON><PERSON><PERSON> deleted successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms deletion', function () {", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}"]}}, "response": []}]}, {"name": "⚙️ Specifications Management", "item": [{"name": "Create Specification", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('created_specification_id', response.data.id);", "        console.log('✅ Specification created with ID: ' + response.data.id);", "    }", "}", "pm.test('Specification created successfully', function () { pm.response.to.have.status(201); });"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"laptop_id\": \"{{created_laptop_id}}\",\n    \"processor\": \"Intel Core i9-14900HX\",\n    \"memory\": \"32GB DDR5\",\n    \"storage\": \"1TB SSD\",\n    \"graphics\": \"NVIDIA RTX 4080\",\n    \"display\": \"16\\\" WQXGA 240Hz\",\n    \"operating_system\": \"Windows 11 Pro\"\n}"}, "url": {"raw": "{{base_url}}/api/specifications", "host": ["{{base_url}}"], "path": ["api", "specifications"]}}, "response": []}, {"name": "Get Specification by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Specification retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains specification data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('processor');", "    pm.expect(response.data).to.have.property('memory');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/specifications/{{created_specification_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{created_specification_id}}"]}}, "response": []}, {"name": "Get Specification by Laptop ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Laptop specification retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains specification data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('laptop_id');", "    pm.expect(response.data).to.have.property('processor');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/laptops/{{created_laptop_id}}/specifications", "host": ["{{base_url}}"], "path": ["api", "laptops", "{{created_laptop_id}}", "specifications"]}}, "response": []}, {"name": "Update Specification", "event": [{"listen": "test", "script": {"exec": ["pm.test('Specification updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated specification data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('processor');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"processor\": \"Intel Core i9-14900HX (Updated)\",\n    \"memory\": \"64GB DDR5\",\n    \"storage\": \"2TB SSD\",\n    \"graphics\": \"NVIDIA RTX 4090\"\n}"}, "url": {"raw": "{{base_url}}/api/specifications/{{created_specification_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{created_specification_id}}"]}}, "response": []}, {"name": "Delete Specification", "event": [{"listen": "test", "script": {"exec": ["pm.test('Specification deleted successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms deletion', function () {", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/specifications/{{created_specification_id}}", "host": ["{{base_url}}"], "path": ["api", "specifications", "{{created_specification_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('🚀 Executing request to: ' + pm.request.url);", "", "// Check if access token exists and is not empty", "const accessToken = pm.environment.get('access_token');", "if (accessToken && accessToken !== '') {", "    console.log('✅ Access token found in environment');", "} else {", "    console.log('⚠️ No access token found - make sure to login first');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is reasonable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Log response details", "console.log('📊 Response Status: ' + pm.response.code);", "console.log('⏱️ Response Time: ' + pm.response.responseTime + 'ms');", "", "// Check for common error patterns", "if (pm.response.code >= 400) {", "    console.log('❌ Error Response Body: ' + pm.response.text());", "}"]}}], "variable": [{"key": "collection_version", "value": "1.0.0", "type": "string"}]}