{"id": "platform-rust-api-environment", "name": "Platform Rust API Environment", "values": [{"key": "base_url", "value": "http://127.0.0.1:8386", "description": "Base URL for the Platform Rust API server", "enabled": true}, {"key": "access_token", "value": "", "description": "JWT access token for authenticated requests", "enabled": true}, {"key": "refresh_token", "value": "", "description": "JWT refresh token for token renewal", "enabled": true}, {"key": "user_id", "value": "", "description": "Current authenticated user ID", "enabled": true}, {"key": "admin_access_token", "value": "", "description": "Super admin JWT access token", "enabled": true}, {"key": "admin_refresh_token", "value": "", "description": "Super admin JWT refresh token", "enabled": true}, {"key": "admin_user_id", "value": "", "description": "Super admin user ID", "enabled": true}, {"key": "jwt_payload", "value": "", "description": "Decoded JWT payload for testing roles and permissions", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "description": "Test user email for registration and login", "enabled": true}, {"key": "test_username", "value": "testuser", "description": "Test user username", "enabled": true}, {"key": "test_fullname", "value": "Test User", "description": "Test user full name", "enabled": true}, {"key": "test_password", "value": "TestPass123!", "description": "Test user password", "enabled": true}, {"key": "super_admin_email", "value": "<EMAIL>", "description": "Super admin email (default system admin)", "enabled": true}, {"key": "super_admin_password", "value": "SuperAdmin123!", "description": "Super admin password (default system admin)", "enabled": true}, {"key": "created_user_id", "value": "", "description": "ID of user created during testing", "enabled": true}, {"key": "created_role_id", "value": "", "description": "ID of role created during testing", "enabled": true}, {"key": "created_permission_id", "value": "", "description": "ID of permission created during testing", "enabled": true}, {"key": "created_category_id", "value": "", "description": "ID of category created during testing", "enabled": true}, {"key": "created_laptop_id", "value": "", "description": "ID of laptop created during testing", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-06-29T08:30:00.000Z", "_postman_exported_using": "Postman/11.0.0"}