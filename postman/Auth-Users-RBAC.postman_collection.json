{"info": {"_postman_id": "auth-users-rbac-collection", "name": "Platform Rust - Auth, Users & RBAC", "description": "Authentication, User Management, Roles and Permissions APIs for Platform Rust system.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "Register User (Gets Member Role)", "event": [{"listen": "test", "script": {"exec": ["// Save tokens to environment variables", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('✅ Tokens saved to environment');", "        ", "        // Validate that new user gets member role and basic permissions", "        pm.test('New user gets member role', function () {", "            pm.expect(response.data.user.roles).to.include('member');", "        });", "        ", "        pm.test('Member has profile permissions', function () {", "            pm.expect(response.data.user.permissions).to.include.members(['profile.read', 'profile.update']);", "        });", "    }", "}", "", "// Validate response structure with roles and permissions", "pm.test('Response has correct structure with RBAC', function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('data');", "    pm.expect(response.data).to.have.property('access_token');", "    pm.expect(response.data).to.have.property('refresh_token');", "    pm.expect(response.data).to.have.property('user');", "    pm.expect(response.data.user).to.have.property('roles');", "    pm.expect(response.data.user).to.have.property('permissions');", "    pm.expect(response.data.user.roles).to.be.an('array');", "    pm.expect(response.data.user.permissions).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"username\": \"{{test_username}}\",\n    \"fullname\": \"{{test_fullname}}\",\n    \"password\": \"{{test_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}, "response": []}, {"name": "Login Super Admin", "event": [{"listen": "test", "script": {"exec": ["// Save tokens to a single environment variable for consistency", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        pm.environment.set('user_id', response.data.user.id); // Save current user ID", "        console.log('✅ Super admin login successful, tokens saved');", "        ", "        // Validate super admin has correct role and permissions", "        pm.test('Super admin has correct role', function () {", "            pm.expect(response.data.user.roles).to.include('super_admin');", "        });", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SuperAdmin123!\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "Login Regular User", "event": [{"listen": "test", "script": {"exec": ["// Save tokens to environment variables", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('✅ Login successful, tokens saved');", "    }", "}", "", "pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains RBAC data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('access_token');", "    pm.expect(response.data).to.have.property('refresh_token');", "    pm.expect(response.data.user).to.have.property('roles');", "    pm.expect(response.data.user).to.have.property('permissions');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{test_email}}\",\n    \"password\": \"{{test_password}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "Refresh <PERSON> (Updates Roles/Permissions)", "event": [{"listen": "test", "script": {"exec": ["// Update tokens in environment", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        console.log('✅ Tokens refreshed successfully');", "        ", "        // Validate that refresh token also returns updated roles/permissions", "        pm.test('Refreshed token contains updated RBAC info', function () {", "            pm.expect(response.data.user).to.have.property('roles');", "            pm.expect(response.data.user).to.have.property('permissions');", "        });", "    }", "}", "", "pm.test('Token refresh successful', function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh"]}}, "response": []}, {"name": "Google OAuth <PERSON>gin (Redirect)", "protocolProfileBehavior": {"followRedirects": false}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/oauth/google", "host": ["{{base_url}}"], "path": ["api", "auth", "o<PERSON>h", "google"]}, "description": "Initiates the Google OAuth2 login flow by redirecting the user to Google's consent page. <PERSON><PERSON> will NOT handle the redirect automatically in the test environment (followRedirects is disabled), but this shows the starting point. The server will respond with a 302 Redirect. Copy the 'Location' header from the response and open it in your browser to continue."}, "response": []}, {"name": "Google OAuth <PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Save tokens to environment variables", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.access_token) {", "        pm.environment.set('access_token', response.data.access_token);", "        pm.environment.set('refresh_token', response.data.refresh_token);", "        pm.environment.set('user_id', response.data.user.id);", "        console.log('✅ Google OAuth login successful, tokens saved');", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/oauth/google/callback?code=PASTE_CODE_HERE&state=PASTE_STATE_HERE", "host": ["{{base_url}}"], "path": ["api", "auth", "o<PERSON>h", "google", "callback"], "query": [{"key": "code", "value": "PASTE_CODE_HERE", "description": "The authorization code returned by Google in the URL after you log in."}, {"key": "state", "value": "PASTE_STATE_HERE", "description": "The state parameter for CSRF protection, also in the URL."}]}, "description": "This is the callback URL where Google redirects the user. After completing the login in your browser (from the previous step), copy the `code` and `state` query parameters from your browser's address bar and paste them here to complete the flow and get JWT tokens."}, "response": []}, {"name": "Test JWT Token Content", "event": [{"listen": "prerequest", "script": {"exec": ["// This script will decode JWT token to verify it contains roles and permissions", "const token = pm.environment.get('access_token');", "if (token) {", "    try {", "        // Decode JWT (note: this is just for testing, real apps should verify signature)", "        const base64Payload = token.split('.')[1];", "        const payload = JSON.parse(atob(base64Payload));", "        ", "        console.log('JWT Payload:', JSON.stringify(payload, null, 2));", "        ", "        // Store payload in environment for testing", "        pm.environment.set('jwt_payload', JSON.stringify(payload));", "    } catch (e) {", "        console.log('Error decoding JWT:', e);", "    }", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('JWT contains required fields', function () {", "    const payload = JSON.parse(pm.environment.get('jwt_payload'));", "    pm.expect(payload).to.have.property('sub'); // user ID", "    pm.expect(payload).to.have.property('roles');", "    pm.expect(payload).to.have.property('permissions');", "    pm.expect(payload).to.have.property('token_type');", "    pm.expect(payload.token_type).to.equal('access');", "});", "", "pm.test('JWT roles and permissions are arrays', function () {", "    const payload = JSON.parse(pm.environment.get('jwt_payload'));", "    pm.expect(payload.roles).to.be.an('array');", "    pm.expect(payload.permissions).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}, "response": []}]}, {"name": "👤 User Management", "item": [{"name": "Create User", "event": [{"listen": "test", "script": {"exec": ["// Save created user ID", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('created_user_id', response.data.id);", "        console.log('✅ User created with ID: ' + response.data.id);", "    }", "}", "", "pm.test('User created successfully', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains user data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('email');", "    pm.expect(response.data).to.have.property('username');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"username\": \"newuser123\",\n    \"fullname\": \"New User Full Name\",\n    \"password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}}, "response": []}, {"name": "Get User by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('User retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains user data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('email');", "    pm.expect(response.data).to.have.property('username');", "    pm.expect(response.data).to.have.property('fullname');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}"]}}, "response": []}, {"name": "Get Users with Pagination", "event": [{"listen": "test", "script": {"exec": ["pm.test('Users list retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains pagination info', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('data');", "    pm.expect(response.data).to.have.property('pagination');", "    pm.expect(response.data.pagination).to.have.property('page');", "    pm.expect(response.data.pagination).to.have.property('limit');", "    pm.expect(response.data.pagination).to.have.property('total');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Update User", "event": [{"listen": "test", "script": {"exec": ["pm.test('User updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated user data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('email');", "    pm.expect(response.data).to.have.property('username');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"fullname\": \"Updated Full Name\",\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/users/{{created_user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{created_user_id}}"]}}, "response": []}, {"name": "Delete User", "event": [{"listen": "test", "script": {"exec": ["pm.test('User deleted successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms deletion', function () {", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{created_user_id}}", "host": ["{{base_url}}"], "path": ["api", "users", "{{created_user_id}}"]}}, "response": []}, {"name": "Get User Roles", "event": [{"listen": "test", "script": {"exec": ["pm.test('User roles retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains roles array', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('roles');", "    pm.expect(response.data.roles).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "roles"]}}, "response": []}, {"name": "Replace User Roles", "event": [{"listen": "test", "script": {"exec": ["pm.test('User roles updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated roles', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('roles');", "    pm.expect(response.data.roles).to.be.an('array');", "    pm.expect(response.data.roles.length).to.eql(2); // expecting 2 roles as example", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"roles\": [\"member\", \"admin\"]\n}"}, "url": {"raw": "{{base_url}}/api/users/{{user_id}}/roles", "host": ["{{base_url}}"], "path": ["api", "users", "{{user_id}}", "roles"]}}, "response": []}]}, {"name": "🛡️ Permissions Management", "item": [{"name": "Create Permission", "event": [{"listen": "test", "script": {"exec": ["// Save created permission ID", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('created_permission_id', response.data.id);", "        console.log('✅ Permission created with ID: ' + response.data.id);", "    }", "}", "", "pm.test('Permission created successfully', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains permission data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"users.create\",\n    \"description\": \"Permission to create new users\"\n}"}, "url": {"raw": "{{base_url}}/api/permissions", "host": ["{{base_url}}"], "path": ["api", "permissions"]}}, "response": []}, {"name": "Get Permission by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Permission retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains permission data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "    pm.expect(response.data).to.have.property('description');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions/{{created_permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{created_permission_id}}"]}}, "response": []}, {"name": "Get Permissions with Pagination", "event": [{"listen": "test", "script": {"exec": ["pm.test('Permissions list retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains pagination info', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('data');", "    pm.expect(response.data).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "permissions"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Update Permission", "event": [{"listen": "test", "script": {"exec": ["pm.test('Permission updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated permission data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"users.create.updated\",\n    \"description\": \"Updated permission to create new users\"\n}"}, "url": {"raw": "{{base_url}}/api/permissions/{{created_permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{created_permission_id}}"]}}, "response": []}, {"name": "Delete Permission", "event": [{"listen": "test", "script": {"exec": ["pm.test('Permission deleted successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms deletion', function () {", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/permissions/{{created_permission_id}}", "host": ["{{base_url}}"], "path": ["api", "permissions", "{{created_permission_id}}"]}}, "response": []}, {"name": "📋 Bulk Update Permission Matrix", "event": [{"listen": "test", "script": {"exec": ["pm.test('Permission matrix updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains update statistics', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('changes_applied');", "    pm.expect(response.data).to.have.property('roles_affected');", "    pm.expect(response.data).to.have.property('permissions_affected');", "    pm.expect(response.data.changes_applied).to.be.a('number');", "    pm.expect(response.data.roles_affected).to.be.an('array');", "    pm.expect(response.data.permissions_affected).to.be.an('array');", "});", "", "pm.test('Bulk operation completed atomically', function () {", "    const response = pm.response.json();", "    // If changes_applied > 0, then all requested changes should be applied", "    if (response.data.changes_applied > 0) {", "        console.log('✅ ' + response.data.changes_applied + ' changes applied successfully');", "        console.log('🎭 Roles affected: ' + response.data.roles_affected.join(', '));", "        console.log('🛡️ Permissions affected: ' + response.data.permissions_affected.join(', '));", "    }", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"changes\": [\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{created_permission_id}}\",\n            \"granted\": true\n        },\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{another_permission_id}}\",\n            \"granted\": false\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/permissions/matrix", "host": ["{{base_url}}"], "path": ["api", "permissions", "matrix"]}}, "response": []}, {"name": "📋 Example: Grant Multiple Permissions", "event": [{"listen": "test", "script": {"exec": ["pm.test('Multiple grants successful', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('All permissions granted', function () {", "    const response = pm.response.json();", "    pm.expect(response.data.changes_applied).to.be.at.least(3);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"changes\": [\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{created_permission_id}}\",\n            \"granted\": true\n        },\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{another_permission_id}}\",\n            \"granted\": true\n        },\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{third_permission_id}}\",\n            \"granted\": true\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/permissions/matrix", "host": ["{{base_url}}"], "path": ["api", "permissions", "matrix"]}}, "response": []}, {"name": "📋 Example: Revoke All Permissions", "event": [{"listen": "test", "script": {"exec": ["pm.test('Revoke permissions successful', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Permissions revoked from role', function () {", "    const response = pm.response.json();", "    pm.expect(response.data.changes_applied).to.be.at.least(1);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"changes\": [\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{created_permission_id}}\",\n            \"granted\": false\n        },\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{another_permission_id}}\",\n            \"granted\": false\n        },\n        {\n            \"role_id\": \"{{created_role_id}}\",\n            \"permission_id\": \"{{third_permission_id}}\",\n            \"granted\": false\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/permissions/matrix", "host": ["{{base_url}}"], "path": ["api", "permissions", "matrix"]}}, "response": []}]}, {"name": "🎭 Roles Management", "item": [{"name": "Create Role", "event": [{"listen": "test", "script": {"exec": ["// Save created role ID", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.id) {", "        pm.environment.set('created_role_id', response.data.id);", "        console.log('✅ Role created with ID: ' + response.data.id);", "    }", "}", "", "pm.test('Role created successfully', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response contains role data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"admin\",\n    \"description\": \"Administrator role with full access\"\n}"}, "url": {"raw": "{{base_url}}/api/roles", "host": ["{{base_url}}"], "path": ["api", "roles"]}}, "response": []}, {"name": "Get Role by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Role retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains role data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "    pm.expect(response.data).to.have.property('description');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{created_role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{created_role_id}}"]}}, "response": []}, {"name": "Get Roles with Pagination", "event": [{"listen": "test", "script": {"exec": ["pm.test('Roles list retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains pagination info', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('data');", "    pm.expect(response.data).to.have.property('pagination');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "roles"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Update Role", "event": [{"listen": "test", "script": {"exec": ["pm.test('Role updated successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains updated role data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('name');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-API-Type", "value": "private"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"super_admin\",\n    \"description\": \"Super administrator role with enhanced privileges\"\n}"}, "url": {"raw": "{{base_url}}/api/roles/{{created_role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{created_role_id}}"]}}, "response": []}, {"name": "Delete Role", "event": [{"listen": "test", "script": {"exec": ["pm.test('Role deleted successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response confirms deletion', function () {", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/roles/{{created_role_id}}", "host": ["{{base_url}}"], "path": ["api", "roles", "{{created_role_id}}"]}}, "response": []}, {"name": "Get Role Permissions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/roles/{{created_role_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "roles", "{{created_role_id}}", "permissions"]}}, "response": []}, {"name": "Replace Role Permissions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"permission_ids\": [\n        \"{{permission_read_id}}\",\n        \"{{permission_write_id}}\"\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/roles/{{created_role_id}}/permissions", "host": ["{{base_url}}"], "path": ["api", "roles", "{{created_role_id}}", "permissions"]}}, "response": []}]}, {"name": "👤 Profile Management", "item": [{"name": "Get My Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test('Profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains profile data', function () {", "    const response = pm.response.json();", "    pm.expect(response.data).to.have.property('id');", "    pm.expect(response.data).to.have.property('email');", "    pm.expect(response.data).to.have.property('username');", "    pm.expect(response.data).to.have.property('fullname');", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "X-API-Type", "value": "private"}], "url": {"raw": "{{base_url}}/api/profile/me", "host": ["{{base_url}}"], "path": ["api", "profile", "me"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('🚀 Executing request to: ' + pm.request.url);", "", "// Check if access token exists and is not empty", "const accessToken = pm.environment.get('access_token');", "if (accessToken && accessToken !== '') {", "    console.log('✅ Access token found in environment');", "} else {", "    console.log('⚠️ No access token found - make sure to login first');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is reasonable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Log response details", "console.log('📊 Response Status: ' + pm.response.code);", "console.log('⏱️ Response Time: ' + pm.response.responseTime + 'ms');", "", "// Check for common error patterns", "if (pm.response.code >= 400) {", "    console.log('❌ Error Response Body: ' + pm.response.text());", "}"]}}], "variable": [{"key": "collection_version", "value": "1.0.0", "type": "string"}]}