{"info": {"_postman_id": "system-utils-collection", "name": "Platform Rust - System & Utilities", "description": "Health checks, Email testing and other utility APIs for Platform Rust system.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔍 System Health", "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test('Health check successful', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is OK', function () {", "    pm.expect(pm.response.text()).to.eql('OK');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}]}, {"name": "📧 Email Testing", "item": [{"name": "Test Send Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"<EMAIL>\",\n  \"subject\": \"Test Email\",\n  \"html_body\": \"<b>Hello from Platform Rust!</b>\",\n  \"text_body\": \"Hello from Platform Rust!\"\n}"}, "url": {"raw": "{{base_url}}/api/email/test", "host": ["{{base_url}}"], "path": ["api", "email", "test"]}}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Email sent successfully', function () {", "    pm.response.to.have.status(200);", "    pm.expect(pm.response.text()).to.include('Email sent');", "});"], "type": "text/javascript"}}], "description": "Test API gửi email trự<PERSON> tiếp. Dùng để kiểm tra cấu hình SMTP hoặc debug log."}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script", "console.log('🚀 Executing request to: ' + pm.request.url);", "", "// Check if access token exists and is not empty", "const accessToken = pm.environment.get('access_token');", "if (accessToken && accessToken !== '') {", "    console.log('✅ Access token found in environment');", "} else {", "    console.log('⚠️ No access token found - make sure to login first');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script", "pm.test('Response time is reasonable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Log response details", "console.log('📊 Response Status: ' + pm.response.code);", "console.log('⏱️ Response Time: ' + pm.response.responseTime + 'ms');", "", "// Check for common error patterns", "if (pm.response.code >= 400) {", "    console.log('❌ Error Response Body: ' + pm.response.text());", "}"]}}], "variable": [{"key": "collection_version", "value": "1.0.0", "type": "string"}]}