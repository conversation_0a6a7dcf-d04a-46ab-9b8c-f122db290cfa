# Platform Rust API - Postman Collection

Complete Postman collection for testing Platform Rust RBAC system with authentication, users, roles, and permissions management.

## 🚀 **Quick Start**

### 1. **Import Collection & Environment**
```bash
1. Import: Platform-Rust-API.postman_collection.json  
2. Import: Platform-Rust-API.postman_environment.json
3. Select the environment: "Platform Rust API Environment"
```

### 2. **Set Environment Variables**
Update these variables in your environment:
- `base_url`: `http://localhost:8386` (default server port)
- `test_email`: Your test email for registration
- `test_username`: Your test username  
- `test_fullname`: Your test full name
- `test_password`: Your test password

## 📋 **Collection Structure**

### 🔐 **Authentication**
- **Register User** - Creates new user with `member` role automatically
- **Login Super Admin** - Login with admin credentials, saves tokens
- **Login Regular User** - Login with test user credentials  
- **Refresh Token** - Refresh expired access tokens

### 🛡️ **Permissions Management**
- **Create Permission** - Create new permission
- **Get Permission by ID** - Retrieve single permission
- **Get Permissions with Pagination** - List all permissions
- **Update Permission** - Modify permission details
- **Delete Permission** - Remove permission
- **📋 Bulk Update Permission Matrix** - ⭐ **NEW!** Bulk grant/revoke permissions
- **📋 Example: Grant Multiple Permissions** - ⭐ **NEW!** Demo multiple grants
- **📋 Example: Revoke All Permissions** - ⭐ **NEW!** Demo bulk revoke

### 🎭 **Roles Management**  
- **Create Role** - Create new role
- **Get Role by ID** - Retrieve single role
- **Get Roles with Pagination** - List all roles
- **Update Role** - Modify role details
- **Delete Role** - Remove role
- **Get Role Permissions** - View permissions assigned to role
- **Replace Role Permissions** - Replace all role permissions at once

### 👤 **Users Management**
- **Create User** - Create new user with role assignment
- **Get User by ID** - Retrieve user with role/permission info
- **Get Users with Pagination** - List all users
- **Update User** - Modify user details
- **Delete User** - Remove user
- **Assign User Role** - Add role to user
- **Remove User Role** - Remove role from user

## ⭐ **NEW: Bulk Permission Matrix API**

### **Endpoint**: `PUT /api/permissions/matrix`

**Purpose**: Update multiple role-permission assignments in a single atomic operation.

### **Features**:
- ✅ **Bulk Operations** - Multiple changes in one request
- ✅ **Atomic Transactions** - All succeed or all fail
- ✅ **Mixed Operations** - Grant and revoke in same request  
- ✅ **Validation** - Duplicate detection, existence checks
- ✅ **Statistics** - Response shows changes applied and affected entities

### **Request Format**:
```json
{
  "changes": [
    {
      "role_id": "uuid-of-role",
      "permission_id": "uuid-of-permission", 
      "granted": true  // true = grant, false = revoke
    },
    {
      "role_id": "uuid-of-role",
      "permission_id": "another-uuid",
      "granted": false  // revoke permission
    }
  ]
}
```

### **Response Format**:
```json
{
  "timestamp": "2025-07-05T17:19:00.813751+00:00",
  "status": 200,
  "code": "ROLE04", 
  "message": "Permission matrix updated successfully",
  "data": {
    "changes_applied": 2,
    "roles_affected": ["admin", "member"],
    "permissions_affected": ["users:create", "users:read"]
  },
  "error": null
}
```

### **Validation Rules**:
- ✅ 1-100 changes per request
- ✅ No duplicate role_id + permission_id combinations
- ✅ All roles and permissions must exist
- ✅ Authentication required with `permissions:update` permission

### **Example Use Cases**:

#### **1. Setup Role Permissions** (Grant Multiple)
```json
{
  "changes": [
    {"role_id": "admin-uuid", "permission_id": "users:create", "granted": true},
    {"role_id": "admin-uuid", "permission_id": "users:update", "granted": true},
    {"role_id": "admin-uuid", "permission_id": "users:delete", "granted": true}
  ]
}
```

#### **2. Remove All Permissions** (Bulk Revoke)
```json
{
  "changes": [
    {"role_id": "member-uuid", "permission_id": "admin:all", "granted": false},
    {"role_id": "member-uuid", "permission_id": "users:create", "granted": false}
  ]
}
```

#### **3. Mixed Operations** (Grant + Revoke)
```json
{
  "changes": [
    {"role_id": "moderator-uuid", "permission_id": "users:read", "granted": true},
    {"role_id": "moderator-uuid", "permission_id": "admin:all", "granted": false}
  ]
}
```

## 🔧 **Authentication Setup**

### **Super Admin Credentials**:
- **Email**: `<EMAIL>`
- **Password**: `SuperAdmin123!`

### **Test Users** (Created during initialization):
- `<EMAIL>` / Password: `TestPass123!`
- `<EMAIL>` / Password: `TestPass123!` 
- `<EMAIL>` / Password: `TestPass123!`

## 🧪 **Testing Flow**

### **Recommended Test Sequence**:
1. **🔐 Login Super Admin** - Get admin token
2. **🛡️ Create Permission** - Create test permission
3. **🎭 Create Role** - Create test role
4. **📋 Bulk Update Permission Matrix** - Test bulk operations
5. **🎭 Get Role Permissions** - Verify changes applied
6. **📦 Create Category** - Create a test category
7. **💻 Create Laptop** - Create a test laptop in the category
8. **👤 Create User** - Create user with role
9. **🔐 Login Regular User** - Test user permissions

### **Environment Variables Auto-Population**:
- `access_token` - Auto-saved after login
- `refresh_token` - Auto-saved after login  
- `user_id` - Auto-saved after login/registration
- `created_role_id` - Auto-saved after role creation
- `created_permission_id` - Auto-saved after permission creation
- `created_category_id` - Auto-saved after category creation
- `created_laptop_id` - Auto-saved after laptop creation

## 🎯 **For Frontend Teams**

### **Permission Matrix Integration**:
1. **Get all roles**: `GET /api/roles?page=1&limit=100`
2. **Get all permissions**: `GET /api/permissions?page=1&limit=100`
3. **Get role permissions**: `GET /api/roles/{role_id}/permissions`
4. **Update matrix**: `PUT /api/permissions/matrix` with bulk changes
5. **Verify changes**: Re-fetch role permissions

### **Matrix UI Workflow**:
```javascript
// 1. Load initial data
const roles = await fetchRoles();
const permissions = await fetchPermissions();
const currentMatrix = await fetchRolePermissions(roleId);

// 2. User makes changes in UI matrix
const changes = collectMatrixChanges(); // Array of {role_id, permission_id, granted}

// 3. Send bulk update
const result = await updatePermissionMatrix({ changes });

// 4. Show results
console.log(`✅ ${result.data.changes_applied} changes applied`);
console.log(`🎭 Roles: ${result.data.roles_affected.join(', ')}`);
console.log(`🛡️ Permissions: ${result.data.permissions_affected.join(', ')}`);
```

## 📚 **API Documentation**

- **Swagger UI**: `http://localhost:8386/swagger-ui`
- **OpenAPI Spec**: `http://localhost:8386/api-docs/openapi.json`

## 🔍 **Troubleshooting**

### **Common Issues**:

1. **Token Expired** 
   - Solution: Run "Refresh Token" request

2. **Permission Denied**
   - Solution: Login as Super Admin for admin operations

3. **Role/Permission Not Found**
   - Solution: Create role/permission first, check UUIDs

4. **Duplicate Changes Error**
   - Solution: Remove duplicate role_id + permission_id combinations

5. **Server Not Running**
   - Solution: Start server with `cargo run`, check port 8386

### **Debug Steps**:
1. Check server health: `GET {{base_url}}/health`
2. Verify authentication: Check `access_token` environment variable  
3. Check permissions: Login response shows user roles/permissions
4. Validate UUIDs: Use "Get Roles/Permissions" to get valid IDs

---

🎉 **Happy Testing!** The Bulk Permission Matrix API is now ready for integration. 