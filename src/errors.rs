use crate::constants::system as system_codes;
use crate::constants::auth as auth_codes;
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use chrono::Utc;
use serde_json::json;

#[derive(thiserror::Error, Debug)]
pub enum AppError {
    #[error("Database error: {0}")]
    Diesel(#[from] diesel::result::Error),

    #[error("Connection pool error: {0}")]
    Pool(#[from] diesel::r2d2::PoolError),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Validation errors")]
    ValidationDetailed(crate::utils::validation::ValidationResult),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Internal server error")]
    Internal(#[from] anyhow::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Forbidden: {0}")]
    Forbidden(String),

    #[error("Conflict: {0}")]
    Conflict(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Not implemented: {0}")]
    NotImplemented(String),

    #[error("Token expired")]
    TokenExpired,

    #[error("Permission or roles changed")]
    PermissionChanged,
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message, error_code) = match &self {
            AppError::TokenExpired => (
                StatusCode::UNAUTHORIZED,
                "Access token expired",
                auth_codes::TOKEN_EXPIRED,
            ),
            AppError::PermissionChanged => (
                StatusCode::UNAUTHORIZED,
                "Permission changed or revoked",
                auth_codes::PERMISSION_CHANGED,
            ),
            AppError::Diesel(err) => {
                tracing::error!("Database error: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Database error occurred",
                    system_codes::ERROR_DATABASE,
                )
            }
            AppError::Pool(err) => {
                tracing::error!("Connection pool error: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Database connection error",
                    system_codes::ERROR_DATABASE,
                )
            }
            AppError::Validation(ref err) => (
                StatusCode::BAD_REQUEST,
                err.as_str(),
                system_codes::ERROR_VALIDATION,
            ),
            AppError::ValidationDetailed(_) => (
                StatusCode::BAD_REQUEST,
                "Validation failed",
                system_codes::ERROR_VALIDATION,
            ),
            AppError::NotFound(ref err) => (
                StatusCode::NOT_FOUND,
                err.as_str(),
                system_codes::ERROR_NOT_FOUND,
            ),
            AppError::Unauthorized(ref err) => (
                StatusCode::UNAUTHORIZED,
                err.as_str(),
                system_codes::ERROR_UNAUTHORIZED,
            ),
            AppError::Forbidden(ref err) => (
                StatusCode::FORBIDDEN,
                err.as_str(),
                system_codes::ERROR_FORBIDDEN,
            ),
            AppError::Conflict(ref err) => (
                StatusCode::CONFLICT,
                err.as_str(),
                system_codes::ERROR_CONFLICT,
            ),
            AppError::BadRequest(ref err) => (
                StatusCode::BAD_REQUEST,
                err.as_str(),
                system_codes::ERROR_BAD_REQUEST,
            ),
            AppError::NotImplemented(ref err) => (
                StatusCode::NOT_IMPLEMENTED,
                err.as_str(),
                system_codes::ERROR_INTERNAL,
            ),
            AppError::Internal(err) => {
                tracing::error!("Internal error: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Internal server error",
                    system_codes::ERROR_INTERNAL,
                )
            }
            AppError::Serialization(err) => {
                tracing::error!("Serialization error: {}", err);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "Serialization error",
                    system_codes::ERROR_INTERNAL,
                )
            }
        };

        let body = match &self {
            AppError::ValidationDetailed(validation_result) => Json(json!({
                "timestamp": Utc::now().to_rfc3339(),
                "path": "unknown",
                "status": status.as_u16(),
                "code": error_code,
                "message": "Validation failed",
                "data": null,
                "error": validation_result.to_structured_error(),
            })),
            _ => {
                // Check for custom auth errors
                if error_code == auth_codes::TOKEN_EXPIRED || error_code == auth_codes::PERMISSION_CHANGED {
                    let body = Json(json!({
                        "success": false,
                        "message": error_message,
                        "code": error_code,
                        "timestamp": Utc::now().to_rfc3339(),
                    }));
                    return (status, body).into_response();
                }

                Json(json!({
                    "timestamp": Utc::now().to_rfc3339(),
                    "path": "unknown",
                    "status": status.as_u16(),
                    "code": error_code,
                    "message": "Error occurred",
                    "data": null,
                    "error": error_message,
                }))
            }
        };

        (status, body).into_response()
    }
}

pub type Result<T> = std::result::Result<T, AppError>;
