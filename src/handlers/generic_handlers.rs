use crate::{
    errors::Result,
    repository::{
        base_repository::Entity,
        generic_pagination::PaginatedResponse,
    },
    utils::response_helpers::ResponseHelper,
};
use axum::response::IntoResponse;
use serde::Serialize;

/// Generic CRUD handler helper - removed trait with patterns in function signatures

/// Generic handler helper functions
pub struct HandlerHelper;

impl HandlerHelper {
    /// Create a successful creation response
    pub fn created_response<T: Serialize>(
        entity: T,
        path: &str,
        success_code: &str,
        message: &str,
    ) -> impl IntoResponse {
        ResponseHelper::entity_created(path, success_code, message, entity)
    }

    /// Create a successful get response
    pub fn success_response<T: Serialize>(
        entity: T,
        path: &str,
        success_code: &str,
        message: &str,
    ) -> impl IntoResponse {
        ResponseHelper::entity_retrieved(path, None, success_code, message, entity)
    }

    /// Create a successful update response
    pub fn updated_response<T: Serialize>(
        entity: T,
        path: &str,
        success_code: &str,
        message: &str,
    ) -> impl IntoResponse {
        ResponseHelper::entity_retrieved(path, None, success_code, message, entity)
    }

    /// Create a successful delete response
    pub fn deleted_response(path: &str, id: &str, success_code: &str, message: &str) -> impl IntoResponse {
        ResponseHelper::entity_deleted(path, id, success_code, message)
    }

    /// Create a successful paginated response
    pub fn paginated_response<T: Serialize>(
        data: PaginatedResponse<T>,
        path: &str,
        success_code: &str,
        message: &str,
    ) -> impl IntoResponse {
        ResponseHelper::entity_list(path, success_code, message, data, None)
    }
}

/// Macro to implement basic CRUD handlers for an entity
#[macro_export]
macro_rules! impl_crud_handlers {
    (
        $handler_struct:ident,
        $entity:ty,
        $service:ty,
        $create_req:ty,
        $update_req:ty,
        $pagination_req:ty,
        $api_path:expr,
        $success_codes:expr
    ) => {
        pub struct $handler_struct;

        impl $handler_struct {
            pub async fn create(
                State(service): State<std::sync::Arc<$service>>,
                Json(request): Json<$create_req>,
            ) -> crate::errors::Result<impl axum::response::IntoResponse> {
                let entity = service.create(request).await?;
                Ok(crate::handlers::generic_handlers::HandlerHelper::created_response(
                    entity,
                    $api_path,
                    $success_codes.create,
                    concat!("Successfully created ", stringify!($entity)),
                ))
            }

            pub async fn get_by_id(
                State(service): State<std::sync::Arc<$service>>,
                Path(id): Path<<$entity as crate::repository::base_repository::Entity>::Id>,
            ) -> crate::errors::Result<impl axum::response::IntoResponse> {
                let entity = service.get_by_id(&id).await?;
                Ok(crate::handlers::generic_handlers::HandlerHelper::success_response(
                    entity,
                    $success_codes.get,
                    concat!("Successfully retrieved ", stringify!($entity)),
                ))
            }

            pub async fn update(
                State(service): State<std::sync::Arc<$service>>,
                Path(id): Path<<$entity as crate::repository::base_repository::Entity>::Id>,
                Json(request): Json<$update_req>,
            ) -> crate::errors::Result<impl axum::response::IntoResponse> {
                let entity = service.update(&id, request).await?;
                Ok(crate::handlers::generic_handlers::HandlerHelper::updated_response(
                    entity,
                    $success_codes.update,
                    concat!("Successfully updated ", stringify!($entity)),
                ))
            }

            pub async fn delete(
                State(service): State<std::sync::Arc<$service>>,
                Path(id): Path<<$entity as crate::repository::base_repository::Entity>::Id>,
            ) -> crate::errors::Result<impl axum::response::IntoResponse> {
                service.delete(&id).await?;
                Ok(crate::handlers::generic_handlers::HandlerHelper::deleted_response(
                    $success_codes.delete,
                    concat!("Successfully deleted ", stringify!($entity)),
                ))
            }

            pub async fn get_paginated(
                State(service): State<std::sync::Arc<$service>>,
                Query(pagination): Query<$pagination_req>,
            ) -> crate::errors::Result<impl axum::response::IntoResponse> {
                let result = service.get_paginated(pagination).await?;
                Ok(crate::handlers::generic_handlers::HandlerHelper::paginated_response(
                    result,
                    $success_codes.list,
                    concat!("Successfully retrieved ", stringify!($entity), " list"),
                ))
            }
        }
    };
}

/// Success codes structure for CRUD operations
pub struct CrudSuccessCodes {
    pub create: &'static str,
    pub get: &'static str,
    pub update: &'static str,
    pub delete: &'static str,
    pub list: &'static str,
}

/// Helper trait for services used in generic handlers
#[allow(async_fn_in_trait)]
pub trait GenericCrudServiceTrait<E, CreateReq, UpdateReq, PaginationReq>: Send + Sync
where
    E: Entity,
{
    async fn create(&self, request: CreateReq) -> Result<E>;
    async fn get_by_id(&self, id: &E::Id) -> Result<E>;
    async fn update(&self, id: &E::Id, request: UpdateReq) -> Result<E>;
    async fn delete(&self, id: &E::Id) -> Result<()>;
    async fn get_paginated(&self, request: PaginationReq) -> Result<PaginatedResponse<E>>;
}

/// Generic route builder helper
pub struct RouteBuilder;

impl RouteBuilder {
    /// Build standard CRUD routes for an entity - simplified
    pub fn build_crud_routes() -> axum::Router {
        axum::Router::new()
            // Routes will be added by specific implementations
    }
}

/// Example usage of the generic handler system
#[cfg(test)]
mod example_usage {
    use super::*;
    use crate::modules::user::models::{CreateUserRequest, UpdateUserRequest, UserPaginationRequest, User};

    // Example success codes
    const USER_SUCCESS_CODES: CrudSuccessCodes = CrudSuccessCodes {
        create: "USER_CREATED",
        get: "USER_RETRIEVED",
        update: "USER_UPDATED", 
        delete: "USER_DELETED",
        list: "USERS_RETRIEVED",
    };

    // Example handler implementation using the macro
    crate::impl_crud_handlers!(
        UserCrudHandler,
        User,
        dyn crate::modules::user::service_trait::UserServiceTrait,
        CreateUserRequest,
        UpdateUserRequest,
        UserPaginationRequest,
        "/api/users",
        USER_SUCCESS_CODES
    );

    // Example of how to build routes
    pub fn user_routes() -> axum::Router<crate::container::AppState> {
        RouteBuilder::build_crud_routes::<UserCrudHandler>()
    }
}
