pub const API_USERS_PATH: &str = "/api/users";
pub const API_PERMISSIONS_PATH: &str = "/api/permissions";
pub const API_ROLES_PATH: &str = "/api/roles";
pub const API_CATEGORIES_PATH: &str = "/api/categories";

// Laptop API paths
pub const API_LAPTOPS_PATH: &str = "/api/laptops";
pub const API_SPECIFICATIONS_PATH: &str = "/api/specifications";
pub const API_PRICES_PATH: &str = "/api/prices";

pub const HEADER_API_TYPE: &str = "X-API-Type";

// ===== SERVICE ERROR CODES =====

// System Error Codes
pub mod system {
    pub const ERROR_DATABASE: &str = "SYS01";
    pub const ERROR_INTERNAL: &str = "SYS02";
    pub const ERROR_VALIDATION: &str = "SYS03";
    pub const ERROR_UNAUTHORIZED: &str = "SYS04";
    pub const ERROR_FORBIDDEN: &str = "SYS05";
    pub const ERROR_NOT_FOUND: &str = "SYS06";
    pub const ERROR_CONFLICT: &str = "SYS07";
    pub const ERROR_BAD_REQUEST: &str = "SYS08";
}

// Auth-specific Error Codes
pub mod auth {
    pub const TOKEN_EXPIRED: &str = "AUTH01";
    pub const PERMISSION_CHANGED: &str = "AUTH02";
}
