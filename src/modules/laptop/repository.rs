use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::laptop::models::{
        DieselLaptop, DieselPrice, DieselSpecification, LaptopPaginationRequest,
        NewLaptop, NewPrice, NewSpecification, PaginatedLaptopsDetailed,
        PaginatedLaptopsFull, PaginatedLaptopsPublic, Price, Specification,
        LaptopDetailedView, LaptopFullView, LaptopPublicView,
    },
    repository::base_repository::AsyncRepository,
    schema::{laptops, prices, specifications},
    utils::pagination::PaginationMeta,
};
use async_trait::async_trait;

use diesel::prelude::*;
use std::sync::Arc;
use uuid::Uuid;

// ===== LAPTOP REPOSITORY =====

#[async_trait]
pub trait LaptopRepositoryTrait: Send + Sync {
    // Basic CRUD operations
    async fn create(&self, laptop: NewLaptop) -> Result<LaptopFullView>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<LaptopFullView>>;
    async fn find_by_slug(&self, slug: &str) -> Result<Option<LaptopFullView>>;
    async fn update(&self, id: &Uuid, laptop: NewLaptop) -> Result<LaptopFullView>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    
    // Pagination operations
    async fn get_public_with_pagination(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsPublic>;
    async fn get_detailed_with_pagination(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsDetailed>;
    async fn get_full_with_pagination(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsFull>;
    
    // Status operations
    async fn update_status(&self, id: &Uuid, status: &str, updated_by: &Uuid) -> Result<()>;
    async fn update_featured(&self, id: &Uuid, is_featured: bool, updated_by: &Uuid) -> Result<()>;
    async fn increment_view_count(&self, id: &Uuid) -> Result<()>;
    
    // Validation operations
    async fn exists_by_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
    async fn exists_by_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
}

pub type DynLaptopRepo = Arc<dyn LaptopRepositoryTrait>;

#[derive(Clone)]
pub struct DieselLaptopRepository {
    base: AsyncRepository,
}

impl DieselLaptopRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl LaptopRepositoryTrait for DieselLaptopRepository {
    async fn create(&self, laptop: NewLaptop) -> Result<LaptopFullView> {
        self.base
            .execute_transaction(move |conn| {
                let inserted_laptop: DieselLaptop = diesel::insert_into(laptops::table)
                    .values(&laptop)
                    .get_result(conn)?;

                Ok(LaptopFullView::from(inserted_laptop))
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<LaptopFullView>> {
        let id = *id;
        
        self.base
            .execute_readonly(move |conn| {
                let laptop = laptops::table
                    .filter(laptops::id.eq(&id))
                    .first::<DieselLaptop>(conn)
                    .optional()?;

                Ok(laptop.map(LaptopFullView::from))
            })
            .await
    }

    async fn find_by_slug(&self, slug: &str) -> Result<Option<LaptopFullView>> {
        let slug = slug.to_string();
        
        self.base
            .execute_readonly(move |conn| {
                let laptop = laptops::table
                    .filter(laptops::slug.eq(&slug))
                    .first::<DieselLaptop>(conn)
                    .optional()?;

                Ok(laptop.map(LaptopFullView::from))
            })
            .await
    }

    async fn update(&self, id: &Uuid, laptop: NewLaptop) -> Result<LaptopFullView> {
        let id = *id;
        
        self.base
            .execute_transaction(move |conn| {
                let updated_laptop: DieselLaptop = diesel::update(laptops::table.filter(laptops::id.eq(&id)))
                    .set((
                        laptops::category_id.eq(&laptop.category_id),
                        laptops::brand.eq(&laptop.brand),
                        laptops::model.eq(&laptop.model),
                        laptops::full_name.eq(&laptop.full_name),
                        laptops::slug.eq(&laptop.slug),
                        laptops::sku.eq(&laptop.sku),
                        laptops::market_region.eq(&laptop.market_region),
                        laptops::release_date.eq(&laptop.release_date),
                        laptops::description.eq(&laptop.description),
                        laptops::image_urls.eq(&laptop.image_urls),
                        laptops::is_featured.eq(&laptop.is_featured),
                        laptops::updated_at.eq(chrono::Utc::now()),
                    ))
                    .get_result(conn)?;

                Ok(LaptopFullView::from(updated_laptop))
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        
        self.base
            .execute_transaction(move |conn| {
                let deleted_count = diesel::delete(laptops::table.filter(laptops::id.eq(&id)))
                    .execute(conn)?;

                if deleted_count == 0 {
                    return Err(AppError::NotFound("Laptop not found".to_string()));
                }

                Ok(())
            })
            .await
    }

    async fn get_public_with_pagination(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsPublic> {
        self.base
            .execute_readonly(move |conn| {
                let mut query = laptops::table.into_boxed();

                // Only show published laptops for public API
                query = query.filter(laptops::status.eq("published"));

                // Apply filters
                if let Some(ref search) = request.search {
                    let search_pattern = format!("%{}%", search);
                    query = query.filter(
                        laptops::brand.ilike(search_pattern.clone())
                            .or(laptops::model.ilike(search_pattern.clone()))
                            .or(laptops::full_name.ilike(search_pattern))
                    );
                }

                if let Some(ref brand) = request.brand {
                    query = query.filter(laptops::brand.eq(brand));
                }

                if let Some(category_id) = request.category_id {
                    query = query.filter(laptops::category_id.eq(category_id));
                }

                if let Some(is_featured) = request.is_featured {
                    query = query.filter(laptops::is_featured.eq(is_featured));
                }

                if let Some(ref market_region) = request.market_region {
                    query = query.filter(laptops::market_region.eq(market_region));
                }

                // Get total count by rebuilding query
                let mut count_query = laptops::table.into_boxed();

                if let Some(ref search) = request.search {
                    count_query = count_query.filter(
                        laptops::brand.ilike(format!("%{}%", search))
                            .or(laptops::model.ilike(format!("%{}%", search)))
                            .or(laptops::full_name.ilike(format!("%{}%", search)))
                    );
                }

                if let Some(ref brand) = request.brand {
                    count_query = count_query.filter(laptops::brand.eq(brand));
                }

                if let Some(category_id) = request.category_id {
                    count_query = count_query.filter(laptops::category_id.eq(category_id));
                }

                if let Some(ref status) = request.status {
                    let status_str = match status {
                        crate::modules::laptop::models::LaptopStatus::Draft => "draft",
                        crate::modules::laptop::models::LaptopStatus::Published => "published",
                        crate::modules::laptop::models::LaptopStatus::Archived => "archived",
                    };
                    count_query = count_query.filter(laptops::status.eq(status_str));
                }

                if let Some(is_featured) = request.is_featured {
                    count_query = count_query.filter(laptops::is_featured.eq(is_featured));
                }

                if let Some(ref market_region) = request.market_region {
                    count_query = count_query.filter(laptops::market_region.eq(market_region));
                }

                let total = count_query.count().get_result::<i64>(conn)?;

                // Apply pagination
                let page = request.page.unwrap_or(1);
                let per_page = request.per_page.unwrap_or(10);
                let offset = (page - 1) * per_page;

                let laptops_list = query
                    .order(laptops::created_at.desc())
                    .limit(per_page)
                    .offset(offset)
                    .load::<DieselLaptop>(conn)?;

                let pagination_meta = PaginationMeta::new(page, per_page, total);

                Ok(PaginatedLaptopsPublic {
                    laptops: laptops_list.into_iter().map(LaptopPublicView::from).collect(),
                    meta: pagination_meta,
                })
            })
            .await
    }

    async fn get_detailed_with_pagination(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsDetailed> {
        self.base
            .execute_readonly(move |conn| {
                let mut query = laptops::table.into_boxed();

                // Apply filters
                if let Some(ref search) = request.search {
                    let search_pattern = format!("%{}%", search);
                    query = query.filter(
                        laptops::brand.ilike(search_pattern.clone())
                            .or(laptops::model.ilike(search_pattern.clone()))
                            .or(laptops::full_name.ilike(search_pattern))
                    );
                }

                if let Some(ref brand) = request.brand {
                    query = query.filter(laptops::brand.eq(brand));
                }

                if let Some(category_id) = request.category_id {
                    query = query.filter(laptops::category_id.eq(category_id));
                }

                if let Some(ref status) = request.status {
                    let status_str = match status {
                        crate::modules::laptop::models::LaptopStatus::Draft => "draft",
                        crate::modules::laptop::models::LaptopStatus::Published => "published",
                        crate::modules::laptop::models::LaptopStatus::Archived => "archived",
                    };
                    query = query.filter(laptops::status.eq(status_str));
                }

                if let Some(is_featured) = request.is_featured {
                    query = query.filter(laptops::is_featured.eq(is_featured));
                }

                if let Some(ref market_region) = request.market_region {
                    query = query.filter(laptops::market_region.eq(market_region));
                }

                // Get total count by rebuilding query
                let mut count_query = laptops::table.into_boxed();

                if let Some(ref search) = request.search {
                    count_query = count_query.filter(
                        laptops::brand.ilike(format!("%{}%", search))
                            .or(laptops::model.ilike(format!("%{}%", search)))
                            .or(laptops::full_name.ilike(format!("%{}%", search)))
                    );
                }

                if let Some(ref brand) = request.brand {
                    count_query = count_query.filter(laptops::brand.eq(brand));
                }

                if let Some(category_id) = request.category_id {
                    count_query = count_query.filter(laptops::category_id.eq(category_id));
                }

                if let Some(ref status) = request.status {
                    let status_str = match status {
                        crate::modules::laptop::models::LaptopStatus::Draft => "draft",
                        crate::modules::laptop::models::LaptopStatus::Published => "published",
                        crate::modules::laptop::models::LaptopStatus::Archived => "archived",
                    };
                    count_query = count_query.filter(laptops::status.eq(status_str));
                }

                if let Some(is_featured) = request.is_featured {
                    count_query = count_query.filter(laptops::is_featured.eq(is_featured));
                }

                if let Some(ref market_region) = request.market_region {
                    count_query = count_query.filter(laptops::market_region.eq(market_region));
                }

                let total = count_query.count().get_result::<i64>(conn)?;

                // Apply pagination
                let page = request.page.unwrap_or(1);
                let per_page = request.per_page.unwrap_or(10);
                let offset = (page - 1) * per_page;

                let laptops_list = query
                    .order(laptops::created_at.desc())
                    .limit(per_page)
                    .offset(offset)
                    .load::<DieselLaptop>(conn)?;

                let pagination_meta = PaginationMeta::new(page, per_page, total);

                Ok(PaginatedLaptopsDetailed {
                    laptops: laptops_list.into_iter().map(LaptopDetailedView::from).collect(),
                    meta: pagination_meta,
                })
            })
            .await
    }

    async fn get_full_with_pagination(&self, request: LaptopPaginationRequest) -> Result<PaginatedLaptopsFull> {
        self.base
            .execute_readonly(move |conn| {
                let mut query = laptops::table.into_boxed();

                // Apply filters (same as detailed but with full view)
                if let Some(ref search) = request.search {
                    let search_pattern = format!("%{}%", search);
                    query = query.filter(
                        laptops::brand.ilike(search_pattern.clone())
                            .or(laptops::model.ilike(search_pattern.clone()))
                            .or(laptops::full_name.ilike(search_pattern))
                    );
                }

                if let Some(ref brand) = request.brand {
                    query = query.filter(laptops::brand.eq(brand));
                }

                if let Some(category_id) = request.category_id {
                    query = query.filter(laptops::category_id.eq(category_id));
                }

                if let Some(ref status) = request.status {
                    let status_str = match status {
                        crate::modules::laptop::models::LaptopStatus::Draft => "draft",
                        crate::modules::laptop::models::LaptopStatus::Published => "published",
                        crate::modules::laptop::models::LaptopStatus::Archived => "archived",
                    };
                    query = query.filter(laptops::status.eq(status_str));
                }

                if let Some(is_featured) = request.is_featured {
                    query = query.filter(laptops::is_featured.eq(is_featured));
                }

                if let Some(ref market_region) = request.market_region {
                    query = query.filter(laptops::market_region.eq(market_region));
                }

                // Get total count (clone query to avoid move)
                let total = {
                    let mut count_query = laptops::table.into_boxed();

                    // Apply same filters for count
                    if let Some(ref search) = request.search {
                        let search_pattern = format!("%{}%", search);
                        count_query = count_query.filter(
                            laptops::brand.ilike(search_pattern.clone())
                                .or(laptops::model.ilike(search_pattern.clone()))
                                .or(laptops::full_name.ilike(search_pattern))
                        );
                    }

                    if let Some(ref brand) = request.brand {
                        count_query = count_query.filter(laptops::brand.eq(brand));
                    }

                    if let Some(category_id) = request.category_id {
                        count_query = count_query.filter(laptops::category_id.eq(category_id));
                    }

                    if let Some(ref status) = request.status {
                        let status_str = match status {
                            crate::modules::laptop::models::LaptopStatus::Draft => "draft",
                            crate::modules::laptop::models::LaptopStatus::Published => "published",
                            crate::modules::laptop::models::LaptopStatus::Archived => "archived",
                        };
                        count_query = count_query.filter(laptops::status.eq(status_str));
                    }

                    if let Some(is_featured) = request.is_featured {
                        count_query = count_query.filter(laptops::is_featured.eq(is_featured));
                    }

                    if let Some(ref market_region) = request.market_region {
                        count_query = count_query.filter(laptops::market_region.eq(market_region));
                    }

                    count_query.count().get_result::<i64>(conn)?
                };

                // Apply pagination
                let page = request.page.unwrap_or(1);
                let per_page = request.per_page.unwrap_or(10);
                let offset = (page - 1) * per_page;

                let laptops_list = query
                    .order(laptops::created_at.desc())
                    .limit(per_page)
                    .offset(offset)
                    .load::<DieselLaptop>(conn)?;

                let pagination_meta = PaginationMeta::new(page, per_page, total);

                Ok(PaginatedLaptopsFull {
                    laptops: laptops_list.into_iter().map(LaptopFullView::from).collect(),
                    meta: pagination_meta,
                })
            })
            .await
    }

    async fn update_status(&self, id: &Uuid, status: &str, updated_by: &Uuid) -> Result<()> {
        let id = *id;
        let status = status.to_string();
        let updated_by = *updated_by;

        self.base
            .execute_transaction(move |conn| {
                let updated_count = diesel::update(laptops::table.filter(laptops::id.eq(&id)))
                    .set((
                        laptops::status.eq(&status),
                        laptops::updated_by.eq(&updated_by),
                        laptops::updated_at.eq(chrono::Utc::now()),
                    ))
                    .execute(conn)?;

                if updated_count == 0 {
                    return Err(AppError::NotFound("Laptop not found".to_string()));
                }

                Ok(())
            })
            .await
    }

    async fn update_featured(&self, id: &Uuid, is_featured: bool, updated_by: &Uuid) -> Result<()> {
        let id = *id;
        let updated_by = *updated_by;

        self.base
            .execute_transaction(move |conn| {
                let updated_count = diesel::update(laptops::table.filter(laptops::id.eq(&id)))
                    .set((
                        laptops::is_featured.eq(is_featured),
                        laptops::updated_by.eq(&updated_by),
                        laptops::updated_at.eq(chrono::Utc::now()),
                    ))
                    .execute(conn)?;

                if updated_count == 0 {
                    return Err(AppError::NotFound("Laptop not found".to_string()));
                }

                Ok(())
            })
            .await
    }

    async fn increment_view_count(&self, id: &Uuid) -> Result<()> {
        let id = *id;

        self.base
            .execute_transaction(move |conn| {
                let updated_count = diesel::update(laptops::table.filter(laptops::id.eq(&id)))
                    .set(laptops::view_count.eq(laptops::view_count + 1))
                    .execute(conn)?;

                if updated_count == 0 {
                    return Err(AppError::NotFound("Laptop not found".to_string()));
                }

                Ok(())
            })
            .await
    }

    async fn exists_by_slug(&self, slug: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        let slug = slug.to_string();
        let exclude_id = exclude_id.copied();

        self.base
            .execute_readonly(move |conn| {
                let mut query = laptops::table
                    .filter(laptops::slug.eq(&slug))
                    .into_boxed();

                if let Some(exclude_id) = exclude_id {
                    query = query.filter(laptops::id.ne(exclude_id));
                }

                let count = query.count().get_result::<i64>(conn)?;
                Ok(count > 0)
            })
            .await
    }

    async fn exists_by_sku(&self, sku: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        let sku = sku.to_string();
        let exclude_id = exclude_id.copied();

        self.base
            .execute_readonly(move |conn| {
                let mut query = laptops::table
                    .filter(laptops::sku.eq(&sku))
                    .into_boxed();

                if let Some(exclude_id) = exclude_id {
                    query = query.filter(laptops::id.ne(exclude_id));
                }

                let count = query.count().get_result::<i64>(conn)?;
                Ok(count > 0)
            })
            .await
    }
}

// ===== SPECIFICATION REPOSITORY =====

#[async_trait]
pub trait SpecificationRepositoryTrait: Send + Sync {
    async fn create(&self, spec: NewSpecification) -> Result<Specification>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Specification>>;
    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Option<Specification>>;
    async fn update(&self, id: &Uuid, spec: NewSpecification) -> Result<Specification>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()>;
}

pub type DynSpecificationRepo = Arc<dyn SpecificationRepositoryTrait>;

#[derive(Clone)]
pub struct DieselSpecificationRepository {
    base: AsyncRepository,
}

impl DieselSpecificationRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl SpecificationRepositoryTrait for DieselSpecificationRepository {
    async fn create(&self, spec: NewSpecification) -> Result<Specification> {
        self.base
            .execute_transaction(move |conn| {
                let inserted_spec: DieselSpecification = diesel::insert_into(specifications::table)
                    .values(&spec)
                    .get_result(conn)?;

                Ok(Specification::from(inserted_spec))
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Specification>> {
        let id = *id;

        self.base
            .execute_readonly(move |conn| {
                let spec = specifications::table
                    .filter(specifications::id.eq(&id))
                    .first::<DieselSpecification>(conn)
                    .optional()?;

                Ok(spec.map(Specification::from))
            })
            .await
    }

    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Option<Specification>> {
        let laptop_id = *laptop_id;

        self.base
            .execute_readonly(move |conn| {
                let spec = specifications::table
                    .filter(specifications::laptop_id.eq(&laptop_id))
                    .first::<DieselSpecification>(conn)
                    .optional()?;

                Ok(spec.map(Specification::from))
            })
            .await
    }

    async fn update(&self, id: &Uuid, spec: NewSpecification) -> Result<Specification> {
        let id = *id;

        self.base
            .execute_transaction(move |conn| {
                let updated_spec: DieselSpecification = diesel::update(specifications::table.filter(specifications::id.eq(&id)))
                    .set((
                        specifications::cpu_brand.eq(&spec.cpu_brand),
                        specifications::cpu_model.eq(&spec.cpu_model),
                        specifications::ram_size.eq(&spec.ram_size),
                        specifications::ram_type.eq(&spec.ram_type),
                        specifications::ram_slots_total.eq(&spec.ram_slots_total),
                        specifications::ram_slots_available.eq(&spec.ram_slots_available),
                        specifications::ram_max_capacity.eq(&spec.ram_max_capacity),
                        specifications::ram_soldered.eq(&spec.ram_soldered),
                        specifications::storage_type.eq(&spec.storage_type),
                        specifications::storage_capacity.eq(&spec.storage_capacity),
                        specifications::storage_slots_total.eq(&spec.storage_slots_total),
                        specifications::storage_slots_available.eq(&spec.storage_slots_available),
                        specifications::storage_max_capacity.eq(&spec.storage_max_capacity),
                        specifications::gpu_type.eq(&spec.gpu_type),
                        specifications::gpu_model.eq(&spec.gpu_model),
                        specifications::screen_size.eq(&spec.screen_size),
                        specifications::screen_resolution.eq(&spec.screen_resolution),
                        specifications::refresh_rate.eq(&spec.refresh_rate),
                        specifications::weight.eq(&spec.weight),
                        specifications::operating_system.eq(&spec.operating_system),
                        specifications::updated_at.eq(chrono::Utc::now()),
                    ))
                    .get_result(conn)?;

                Ok(Specification::from(updated_spec))
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;

        self.base
            .execute_transaction(move |conn| {
                let deleted_count = diesel::delete(specifications::table.filter(specifications::id.eq(&id)))
                    .execute(conn)?;

                if deleted_count == 0 {
                    return Err(AppError::NotFound("Specification not found".to_string()));
                }

                Ok(())
            })
            .await
    }

    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        let laptop_id = *laptop_id;

        self.base
            .execute_transaction(move |conn| {
                diesel::delete(specifications::table.filter(specifications::laptop_id.eq(&laptop_id)))
                    .execute(conn)?;

                Ok(())
            })
            .await
    }
}

// ===== PRICE REPOSITORY =====

#[async_trait]
pub trait PriceRepositoryTrait: Send + Sync {
    async fn create(&self, price: NewPrice) -> Result<Price>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Price>>;
    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn find_current_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>>;
    async fn update(&self, id: &Uuid, price: NewPrice) -> Result<Price>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()>;
    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()>;
}

pub type DynPriceRepo = Arc<dyn PriceRepositoryTrait>;

#[derive(Clone)]
pub struct DieselPriceRepository {
    base: AsyncRepository,
}

impl DieselPriceRepository {
    pub fn new(database: Database) -> Self {
        Self {
            base: AsyncRepository::new(database),
        }
    }
}

#[async_trait]
impl PriceRepositoryTrait for DieselPriceRepository {
    async fn create(&self, price: NewPrice) -> Result<Price> {
        self.base
            .execute_transaction(move |conn| {
                let inserted_price: DieselPrice = diesel::insert_into(prices::table)
                    .values(&price)
                    .get_result(conn)?;

                Ok(Price::from(inserted_price))
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<Price>> {
        let id = *id;

        self.base
            .execute_readonly(move |conn| {
                let price = prices::table
                    .filter(prices::id.eq(&id))
                    .first::<DieselPrice>(conn)
                    .optional()?;

                Ok(price.map(Price::from))
            })
            .await
    }

    async fn find_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        let laptop_id = *laptop_id;

        self.base
            .execute_readonly(move |conn| {
                let prices_list = prices::table
                    .filter(prices::laptop_id.eq(&laptop_id))
                    .order(prices::effective_date.desc())
                    .load::<DieselPrice>(conn)?;

                Ok(prices_list.into_iter().map(Price::from).collect())
            })
            .await
    }

    async fn find_current_by_laptop_id(&self, laptop_id: &Uuid) -> Result<Vec<Price>> {
        let laptop_id = *laptop_id;

        self.base
            .execute_readonly(move |conn| {
                let prices_list = prices::table
                    .filter(prices::laptop_id.eq(&laptop_id))
                    .filter(prices::is_current.eq(true))
                    .order(prices::effective_date.desc())
                    .load::<DieselPrice>(conn)?;

                Ok(prices_list.into_iter().map(Price::from).collect())
            })
            .await
    }

    async fn update(&self, id: &Uuid, price: NewPrice) -> Result<Price> {
        let id = *id;

        self.base
            .execute_transaction(move |conn| {
                let updated_price: DieselPrice = diesel::update(prices::table.filter(prices::id.eq(&id)))
                    .set((
                        prices::min_price.eq(&price.min_price),
                        prices::max_price.eq(&price.max_price),
                        prices::currency.eq(&price.currency),
                        prices::source.eq(&price.source),
                        prices::region.eq(&price.region),
                        prices::effective_date.eq(&price.effective_date),
                        prices::is_current.eq(&price.is_current),
                        prices::updated_at.eq(chrono::Utc::now()),
                    ))
                    .get_result(conn)?;

                Ok(Price::from(updated_price))
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;

        self.base
            .execute_transaction(move |conn| {
                let deleted_count = diesel::delete(prices::table.filter(prices::id.eq(&id)))
                    .execute(conn)?;

                if deleted_count == 0 {
                    return Err(AppError::NotFound("Price not found".to_string()));
                }

                Ok(())
            })
            .await
    }

    async fn delete_by_laptop_id(&self, laptop_id: &Uuid) -> Result<()> {
        let laptop_id = *laptop_id;

        self.base
            .execute_transaction(move |conn| {
                diesel::delete(prices::table.filter(prices::laptop_id.eq(&laptop_id)))
                    .execute(conn)?;

                Ok(())
            })
            .await
    }

    async fn set_current_price(&self, laptop_id: &Uuid, price_id: &Uuid) -> Result<()> {
        let laptop_id = *laptop_id;
        let price_id = *price_id;

        self.base
            .execute_transaction(move |conn| {
                // First, set all prices for this laptop to not current
                diesel::update(prices::table.filter(prices::laptop_id.eq(&laptop_id)))
                    .set(prices::is_current.eq(false))
                    .execute(conn)?;

                // Then set the specified price as current
                let updated_count = diesel::update(prices::table.filter(prices::id.eq(&price_id)))
                    .set(prices::is_current.eq(true))
                    .execute(conn)?;

                if updated_count == 0 {
                    return Err(AppError::NotFound("Price not found".to_string()));
                }

                Ok(())
            })
            .await
    }
}
