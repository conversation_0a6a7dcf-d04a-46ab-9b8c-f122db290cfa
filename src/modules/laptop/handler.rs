use crate::{
    constants::{API_LAPTOPS_PATH, API_SPECIFICATIONS_PATH, API_PRICES_PATH, HEADER_API_TYPE},
    errors::{AppError, Result},
    modules::laptop::{
        models::{
            CreateLaptopRequest, CreateSpecificationRequest, CreatePriceRequest,
            LaptopPaginationRequest, UpdateLaptopRequest, UpdatePriceRequest,
            UpdateSpecificationRequest, LaptopFullView, Specification, Price,
        },
        service_trait::LaptopManagementServiceTrait,
    },
    routes::middleware::auth::{AuthenticatedUser, extract_user_from_headers},
    utils::response_helpers::{
        response_constants::{laptop, specification, price},
        ResponseHelper,
    },
    container::ServiceContainer,
    response::error_response,
};
use axum::{
    extract::{Path, Query, State},
    http::HeaderMap,
    response::{IntoResponse, Response},
    Extension, Json,
};
use serde_json;
use std::sync::Arc;
use utoipa::ToSchema;
use uuid::Uuid;

// ===== LAPTOP HANDLERS (Support both Public and Private API via header) =====

/// Get laptops with pagination
/// - Public API (no header or X-API-Type: public): Returns only published laptops with limited fields
/// - Private API (X-API-Type: private + auth): Returns all laptops with detailed/full fields based on permissions
#[utoipa::path(
    get,
    path = "/api/laptops",
    tag = "Laptops",
    params(
        ("page" = Option<i64>, Query, description = "Page number"),
        ("per_page" = Option<i64>, Query, description = "Items per page"),
        ("search" = Option<String>, Query, description = "Search term"),
        ("brand" = Option<String>, Query, description = "Filter by brand"),
        ("category_id" = Option<String>, Query, description = "Filter by category ID"),
        ("status" = Option<String>, Query, description = "Filter by status (private API only)"),
        ("is_featured" = Option<bool>, Query, description = "Filter by featured status"),
        ("market_region" = Option<String>, Query, description = "Filter by market region")
    ),
    responses(
        (status = 200, description = "Laptops retrieved successfully"),
        (status = 400, description = "Invalid query parameters")
    )
)]
pub async fn get_laptops(
    headers: HeaderMap,
    State((laptop_service, container)): State<(Arc<dyn LaptopManagementServiceTrait>, Arc<ServiceContainer>)>,
    Query(pagination): Query<LaptopPaginationRequest>,
    user: Option<Extension<AuthenticatedUser>>,
) -> Result<Response> {
    // Check API type from header
    let api_type = headers
        .get(HEADER_API_TYPE)
        .and_then(|h| h.to_str().ok())
        .unwrap_or("public");

    match api_type {
        "public" => {
            // Public API: only published laptops with limited fields
            let laptops = laptop_service.get_public_laptops(pagination).await?;
            let response = crate::response::success_response(
                API_LAPTOPS_PATH.to_string(),
                laptop::SUCCESS_LIST,
                laptop::MSG_LISTED,
                laptops,
            );
            Ok(response.into_response())
        }
        "private" => {
            // Private API: requires authentication
            // Try to get user from extension first, if not available, extract from headers
            let _user = match user {
                Some(Extension(user)) => user,
                None => {
                    // Extract user from headers manually for public routes
                    extract_user_from_headers(&container, &headers).await?
                }
            };

            // Check if user has admin permissions for full view
            let has_admin_permission = _user
                .permissions
                .iter()
                .any(|p| p == "admin:all" || p == "laptops:read");

            if has_admin_permission {
                // Admin users get full view with audit fields
                let laptops = laptop_service.get_full_laptops(pagination).await?;
                let response = crate::response::success_response(
                    API_LAPTOPS_PATH.to_string(),
                    laptop::SUCCESS_LIST,
                    laptop::MSG_LISTED,
                    laptops,
                );
                Ok(response.into_response())
            } else {
                // Regular authenticated users get detailed view without audit fields
                let laptops = laptop_service.get_detailed_laptops(pagination).await?;
                let response = crate::response::success_response(
                    API_LAPTOPS_PATH.to_string(),
                    laptop::SUCCESS_LIST,
                    laptop::MSG_LISTED,
                    laptops,
                );
                Ok(response.into_response())
            }
        }
        _ => Err(AppError::BadRequest("Invalid X-API-Type header".into())),
    }
}

/// Get laptop by slug
/// - Public API: Returns only if published with limited fields
/// - Private API: Returns any status with detailed/full fields based on permissions
#[utoipa::path(
    get,
    path = "/api/laptops/{slug}",
    tag = "Laptops",
    params(
        ("slug" = String, Path, description = "Laptop slug")
    ),
    responses(
        (status = 200, description = "Laptop retrieved successfully"),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn get_laptop_by_slug(
    headers: HeaderMap,
    State((laptop_service, container)): State<(Arc<dyn LaptopManagementServiceTrait>, Arc<ServiceContainer>)>,
    Path(slug): Path<String>,
    user: Option<Extension<AuthenticatedUser>>,
) -> Result<Response> {
    // Check API type from header
    let api_type = headers
        .get(HEADER_API_TYPE)
        .and_then(|h| h.to_str().ok())
        .unwrap_or("public");

    match api_type {
        "public" => {
            // Public API: only published laptops with limited fields
            match laptop_service.get_public_laptop_by_slug(&slug).await {
                Ok(laptop) => {
                    let path = format!("{}/by-slug/{}", API_LAPTOPS_PATH, slug);
                    let response = crate::response::success_response(
                        path,
                        laptop::SUCCESS_GET,
                        laptop::MSG_RETRIEVED,
                        laptop,
                    );
                    Ok(response.into_response())
                }
                Err(AppError::NotFound(error_msg)) => {
                    let path = format!("{}/by-slug/{}", API_LAPTOPS_PATH, slug);
                    let response = error_response(
                        path,
                        axum::http::StatusCode::NOT_FOUND,
                        "SYS06",
                        &error_msg,
                    );
                    Ok(response.into_response())
                }
                Err(e) => Err(e),
            }
        }
        "private" => {
            // Private API: requires authentication
            // Try to get user from extension first, if not available, extract from headers
            let _user = match user {
                Some(Extension(user)) => user,
                None => {
                    // Extract user from headers manually for public routes
                    extract_user_from_headers(&container, &headers).await?
                }
            };

            // Get full laptop details (any status)
            let laptop = match laptop_service.get_laptop_by_slug(&slug).await {
                Ok(laptop) => laptop,
                Err(AppError::NotFound(error_msg)) => {
                    let path = format!("{}/by-slug/{}", API_LAPTOPS_PATH, slug);
                    let response = error_response(
                        path,
                        axum::http::StatusCode::NOT_FOUND,
                        "SYS06",
                        &error_msg,
                    );
                    return Ok(response.into_response());
                }
                Err(e) => return Err(e),
            };

            // Check if user has admin permissions for full view
            let has_admin_permission = _user
                .permissions
                .iter()
                .any(|p| p == "admin:all" || p == "laptops:read");

            let path = format!("{}/by-slug/{}", API_LAPTOPS_PATH, slug);
            
            if has_admin_permission {
                // Admin users get full view with audit fields
                let response = crate::response::success_response(
                    path,
                    laptop::SUCCESS_GET,
                    laptop::MSG_RETRIEVED,
                    laptop,
                );
                Ok(response.into_response())
            } else {
                // Regular authenticated users get detailed view without audit fields
                let detailed_laptop = crate::modules::laptop::models::LaptopDetailedView::from(
                    crate::modules::laptop::models::DieselLaptop {
                        id: laptop.id,
                        category_id: laptop.category_id,
                        brand: laptop.brand,
                        model: laptop.model,
                        full_name: laptop.full_name,
                        slug: laptop.slug,
                        sku: laptop.sku,
                        market_region: laptop.market_region,
                        release_date: laptop.release_date,
                        description: laptop.description,
                        image_urls: Some(laptop.image_urls.into_iter().map(Some).collect()),
                        status: laptop.status,
                        is_featured: laptop.is_featured,
                        view_count: laptop.view_count,
                        created_by: laptop.created_by,
                        updated_by: laptop.updated_by,
                        created_at: laptop.created_at,
                        updated_at: laptop.updated_at,
                    }
                );
                let response = crate::response::success_response(
                    path,
                    laptop::SUCCESS_GET,
                    laptop::MSG_RETRIEVED,
                    detailed_laptop,
                );
                Ok(response.into_response())
            }
        }
        _ => Err(AppError::BadRequest("Invalid X-API-Type header".into())),
    }
}

/// Increment laptop view count (Public API only)
#[utoipa::path(
    post,
    path = "/api/laptops/{id}/view",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "View count incremented successfully"),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn increment_laptop_view_count(
    State((laptop_service, _container)): State<(Arc<dyn LaptopManagementServiceTrait>, Arc<ServiceContainer>)>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.increment_laptop_view_count(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_VIEW_COUNT,
        laptop::MSG_VIEW_COUNT,
        serde_json::json!({"id": id, "view_incremented": true}),
    ))
}

// ===== MANAGEMENT HANDLERS (Private API - Authentication + Permission Required) =====

/// Create laptop (Private API only)
#[utoipa::path(
    post,
    path = "/api/laptops",
    tag = "Laptops",
    request_body = CreateLaptopRequest,
    responses(
        (status = 201, description = "Laptop created successfully", body = LaptopFullView),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Laptop already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(request): Json<CreateLaptopRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let laptop = laptop_service.create_laptop(request, &user_id).await?;

    Ok(ResponseHelper::entity_created(
        API_LAPTOPS_PATH,
        laptop::SUCCESS_CREATE,
        laptop::MSG_CREATED,
        laptop,
    ))
}

/// Get laptop by ID (Private API only)
#[utoipa::path(
    get,
    path = "/api/laptops/{id}",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop retrieved successfully", body = LaptopFullView),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_laptop_by_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
) -> Result<Response> {
    let laptop = laptop_service.get_laptop_by_id(&id).await?;

    // Check if user has admin permissions for full view
    let has_admin_permission = user
        .permissions
        .iter()
        .any(|p| p == "admin:all" || p == "laptops:read");

    let path = format!("{}/{}", API_LAPTOPS_PATH, id);
    
    if has_admin_permission {
        // Admin users get full view with audit fields
        let response = crate::response::success_response(
            path,
            laptop::SUCCESS_GET,
            laptop::MSG_RETRIEVED,
            laptop,
        );
        Ok(response.into_response())
    } else {
        // Regular authenticated users get detailed view without audit fields
        let detailed_laptop = crate::modules::laptop::models::LaptopDetailedView::from(
            crate::modules::laptop::models::DieselLaptop {
                id: laptop.id,
                category_id: laptop.category_id,
                brand: laptop.brand,
                model: laptop.model,
                full_name: laptop.full_name,
                slug: laptop.slug,
                sku: laptop.sku,
                market_region: laptop.market_region,
                release_date: laptop.release_date,
                description: laptop.description,
                image_urls: Some(laptop.image_urls.into_iter().map(Some).collect()),
                status: laptop.status,
                is_featured: laptop.is_featured,
                view_count: laptop.view_count,
                created_by: laptop.created_by,
                updated_by: laptop.updated_by,
                created_at: laptop.created_at,
                updated_at: laptop.updated_at,
            }
        );
        let response = crate::response::success_response(
            path,
            laptop::SUCCESS_GET,
            laptop::MSG_RETRIEVED,
            detailed_laptop,
        );
        Ok(response.into_response())
    }
}



/// Update laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    request_body = UpdateLaptopRequest,
    responses(
        (status = 200, description = "Laptop updated successfully", body = LaptopFullView),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Laptop not found"),
        (status = 409, description = "Laptop slug/SKU conflict")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdateLaptopRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let laptop = laptop_service.update_laptop(&id, request, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_UPDATE,
        laptop::MSG_UPDATED,
        laptop,
    ))
}

/// Delete laptop (Private API only)
#[utoipa::path(
    delete,
    path = "/api/laptops/{id}",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop deleted successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.delete_laptop(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_LAPTOPS_PATH,
        &id.to_string(),
        laptop::SUCCESS_DELETE,
        laptop::MSG_DELETED,
    ))
}

/// Publish laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}/publish",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop published successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn publish_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    laptop_service.publish_laptop(&id, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_PUBLISH,
        laptop::MSG_PUBLISHED,
        serde_json::json!({"id": id, "status": "published"}),
    ))
}

/// Archive laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}/archive",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Laptop archived successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn archive_laptop(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    laptop_service.archive_laptop(&id, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_ARCHIVE,
        laptop::MSG_ARCHIVED,
        serde_json::json!({"id": id, "status": "archived"}),
    ))
}

/// Set laptop featured status (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{id}/featured",
    tag = "Laptops",
    params(
        ("id" = String, Path, description = "Laptop ID")
    ),
    request_body = inline(SetFeaturedRequest),
    responses(
        (status = 200, description = "Laptop featured status updated successfully"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn set_laptop_featured(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
    Json(request): Json<SetFeaturedRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    laptop_service.set_laptop_featured(&id, request.is_featured, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&id.to_string()),
        laptop::SUCCESS_FEATURE,
        laptop::MSG_FEATURED,
        serde_json::json!({"id": id, "is_featured": request.is_featured}),
    ))
}

// ===== SPECIFICATION HANDLERS =====

/// Create specification
#[utoipa::path(
    post,
    path = "/api/specifications",
    tag = "Specifications",
    request_body = CreateSpecificationRequest,
    responses(
        (status = 201, description = "Specification created successfully", body = Specification),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "Specification already exists for laptop")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_specification(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Json(request): Json<CreateSpecificationRequest>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service.create_specification(request).await?;

    Ok(ResponseHelper::entity_created(
        API_SPECIFICATIONS_PATH,
        specification::SUCCESS_CREATE,
        specification::MSG_CREATED,
        specification,
    ))
}

/// Get specification by ID
#[utoipa::path(
    get,
    path = "/api/specifications/{id}",
    tag = "Specifications",
    params(
        ("id" = String, Path, description = "Specification ID")
    ),
    responses(
        (status = 200, description = "Specification retrieved successfully", body = Specification),
        (status = 404, description = "Specification not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_specification_by_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service.get_specification_by_id(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_SPECIFICATIONS_PATH,
        Some(&id.to_string()),
        specification::SUCCESS_GET,
        specification::MSG_RETRIEVED,
        specification,
    ))
}

/// Update specification (Private API only)
#[utoipa::path(
    put,
    path = "/api/specifications/{id}",
    tag = "Specifications",
    params(
        ("id" = String, Path, description = "Specification ID")
    ),
    request_body = UpdateSpecificationRequest,
    responses(
        (status = 200, description = "Specification updated successfully", body = Specification),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Specification not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_specification(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdateSpecificationRequest>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service.update_specification(&id, request).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_SPECIFICATIONS_PATH,
        Some(&id.to_string()),
        specification::SUCCESS_UPDATE,
        specification::MSG_UPDATED,
        specification,
    ))
}

/// Delete specification (Private API only)
#[utoipa::path(
    delete,
    path = "/api/specifications/{id}",
    tag = "Specifications",
    params(
        ("id" = String, Path, description = "Specification ID")
    ),
    responses(
        (status = 200, description = "Specification deleted successfully"),
        (status = 404, description = "Specification not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_specification(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.delete_specification(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_SPECIFICATIONS_PATH,
        &id.to_string(),
        specification::SUCCESS_DELETE,
        specification::MSG_DELETED,
    ))
}

/// Get specifications by laptop ID (Public API)
#[utoipa::path(
    get,
    path = "/api/laptops/{laptop_id}/specifications",
    tag = "Specifications",
    params(
        ("laptop_id" = String, Path, description = "Laptop ID")
    ),
    responses(
        (status = 200, description = "Specifications retrieved successfully", body = Option<Specification>),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn get_specifications_by_laptop_id(
    State((laptop_service, _container)): State<(Arc<dyn LaptopManagementServiceTrait>, Arc<ServiceContainer>)>,
    Path(laptop_id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let specification = laptop_service.get_specification_by_laptop_id(&laptop_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&format!("{}/specifications", laptop_id)),
        specification::SUCCESS_GET,
        specification::MSG_RETRIEVED,
        specification,
    ))
}

// ===== PRICE HANDLERS =====

/// Create price (Private API only)
#[utoipa::path(
    post,
    path = "/api/prices",
    tag = "Prices",
    request_body = CreatePriceRequest,
    responses(
        (status = 201, description = "Price created successfully", body = Price),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Laptop not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Json(request): Json<CreatePriceRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let price = laptop_service.create_price(request, &user_id).await?;

    Ok(ResponseHelper::entity_created(
        API_PRICES_PATH,
        price::SUCCESS_CREATE,
        price::MSG_CREATED,
        price,
    ))
}

/// Get price by ID (Private API only)
#[utoipa::path(
    get,
    path = "/api/prices/{id}",
    tag = "Prices",
    params(
        ("id" = String, Path, description = "Price ID")
    ),
    responses(
        (status = 200, description = "Price retrieved successfully", body = Price),
        (status = 404, description = "Price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_price_by_id(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    let price = laptop_service.get_price_by_id(&id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_PRICES_PATH,
        Some(&id.to_string()),
        price::SUCCESS_GET,
        price::MSG_RETRIEVED,
        price,
    ))
}

/// Update price (Private API only)
#[utoipa::path(
    put,
    path = "/api/prices/{id}",
    tag = "Prices",
    params(
        ("id" = String, Path, description = "Price ID")
    ),
    request_body = UpdatePriceRequest,
    responses(
        (status = 200, description = "Price updated successfully", body = Price),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "Price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Extension(user): Extension<AuthenticatedUser>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdatePriceRequest>,
) -> Result<impl IntoResponse> {
    let user_id = Uuid::parse_str(&user.user_id)
        .map_err(|_| AppError::BadRequest("Invalid user ID format".into()))?;
    let price = laptop_service.update_price(&id, request, &user_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_PRICES_PATH,
        Some(&id.to_string()),
        price::SUCCESS_UPDATE,
        price::MSG_UPDATED,
        price,
    ))
}

/// Delete price (Private API only)
#[utoipa::path(
    delete,
    path = "/api/prices/{id}",
    tag = "Prices",
    params(
        ("id" = String, Path, description = "Price ID")
    ),
    responses(
        (status = 200, description = "Price deleted successfully"),
        (status = 404, description = "Price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    laptop_service.delete_price(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_PRICES_PATH,
        &id.to_string(),
        price::SUCCESS_DELETE,
        price::MSG_DELETED,
    ))
}

/// Get prices by laptop ID (Public API)
#[utoipa::path(
    get,
    path = "/api/laptops/{laptop_id}/prices",
    tag = "Prices",
    params(
        ("laptop_id" = String, Path, description = "Laptop ID"),
        ("current_only" = Option<bool>, Query, description = "Get only current prices")
    ),
    responses(
        (status = 200, description = "Prices retrieved successfully", body = Vec<Price>),
        (status = 404, description = "Laptop not found")
    )
)]
pub async fn get_prices_by_laptop_id(
    State((laptop_service, _container)): State<(Arc<dyn LaptopManagementServiceTrait>, Arc<ServiceContainer>)>,
    Path(laptop_id): Path<Uuid>,
    Query(params): Query<GetPricesQuery>,
) -> Result<impl IntoResponse> {
    let prices = if params.current_only.unwrap_or(false) {
        laptop_service.get_current_prices_by_laptop_id(&laptop_id).await?
    } else {
        laptop_service.get_prices_by_laptop_id(&laptop_id).await?
    };

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&format!("{}/prices", laptop_id)),
        price::SUCCESS_LIST,
        price::MSG_LISTED,
        prices,
    ))
}

/// Set current price for laptop (Private API only)
#[utoipa::path(
    put,
    path = "/api/laptops/{laptop_id}/prices/{price_id}/set-current",
    tag = "Prices",
    params(
        ("laptop_id" = String, Path, description = "Laptop ID"),
        ("price_id" = String, Path, description = "Price ID")
    ),
    responses(
        (status = 200, description = "Current price set successfully"),
        (status = 400, description = "Price does not belong to laptop"),
        (status = 404, description = "Laptop or price not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn set_current_price(
    State(laptop_service): State<Arc<dyn LaptopManagementServiceTrait>>,
    Path((laptop_id, price_id)): Path<(Uuid, Uuid)>,
) -> Result<impl IntoResponse> {
    laptop_service.set_current_price(&laptop_id, &price_id).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_LAPTOPS_PATH,
        Some(&format!("{}/prices/{}/set-current", laptop_id, price_id)),
        price::SUCCESS_SET_CURRENT,
        price::MSG_SET_CURRENT,
        serde_json::json!({"laptop_id": laptop_id, "price_id": price_id, "set_as_current": true}),
    ))
}

// ===== REQUEST/RESPONSE MODELS =====

#[derive(serde::Deserialize, ToSchema)]
pub struct SetFeaturedRequest {
    pub is_featured: bool,
}

#[derive(serde::Deserialize, ToSchema)]
pub struct GetPricesQuery {
    pub current_only: Option<bool>,
}


