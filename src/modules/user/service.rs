use crate::modules::auth::{
    oauth_providers::OAuthUserInfo,
    services::password_service::{PasswordConfig, PasswordService},
};
use crate::{
    config::CacheConfig,
    database::Database,
    errors::{AppError, Result},
    modules::{
        progression::exp_system::DynExpSystemService,
        redis::RedisServiceTrait,
        role::service_trait::RoleServiceTrait,
        user::{
            models::{
                CreateUserRequest, PaginatedUsersWithRoles, UpdateUserRequest, User,
                UserPaginationRequest, UserWithRoles,
            },
            repository::{CreateUserParams, DynUserRepo},
            service_trait::{
                UserManagementTrait, UserOAuthTrait, UserQueryTrait, UserRoleTrait,
                UserServiceTrait,
            },
            user_role_repository::{DynUserRoleRepo, UserRoleInfo},
        },
    },
    utils::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ValidateRequest},
};

use anyhow;
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use uuid::Uuid;

#[derive(Clone)]
pub struct UserService {
    repo: DynUserRepo,
    user_role_repo: DynUserRoleRepo,
    role_service: Arc<dyn RoleServiceTrait>,
    exp_system_service: DynExpSystemService,
    password_service: PasswordService,
    database: Database,
    redis_service: Option<Arc<dyn RedisServiceTrait>>,
    cache_config: CacheConfig,
}

impl UserService {
    pub fn new(
        repo: DynUserRepo,
        user_role_repo: DynUserRoleRepo,
        role_service: Arc<dyn RoleServiceTrait>,
        exp_system_service: DynExpSystemService,
        database: Database,
        redis_service: Option<Arc<dyn RedisServiceTrait>>,
        cache_config: CacheConfig,
    ) -> Self {
        // Create password service with environment-appropriate configuration
        let password_config = if cfg!(debug_assertions) {
            PasswordConfig::fast() // Fast for development
        } else {
            PasswordConfig::default() // Secure for production
        };
        let password_service = PasswordService::with_config(password_config);

        Self {
            repo,
            user_role_repo,
            role_service,
            exp_system_service,
            password_service,
            database,
            redis_service,
            cache_config,
        }
    }

    /// Generate cache key for permission version
    fn permission_version_cache_key(user_id: &Uuid) -> String {
        format!("user:perm_version:{}", user_id)
    }

    /// Cache TTL for permission version from config
    fn permission_version_ttl(&self) -> Duration {
        self.cache_config.permission_version_ttl()
    }

    /// Invalidate permission version cache for a user
    async fn invalidate_permission_version_cache(&self, user_id: &Uuid) -> Result<()> {
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::permission_version_cache_key(user_id);
            match redis.delete(&cache_key).await {
                Ok(deleted) => {
                    if deleted {
                        tracing::info!(
                            user_id = %user_id,
                            cache_key = %cache_key,
                            "Successfully invalidated permission version cache"
                        );
                    } else {
                        tracing::debug!(
                            user_id = %user_id,
                            cache_key = %cache_key,
                            "Cache key did not exist, no invalidation needed"
                        );
                    }
                }
                Err(e) => {
                    tracing::warn!(
                        user_id = %user_id,
                        cache_key = %cache_key,
                        error = %e,
                        "Failed to invalidate permission version cache"
                    );
                    // Don't fail the operation if cache invalidation fails
                }
            }
        } else {
            tracing::debug!(
                user_id = %user_id,
                "Redis not available, skipping cache invalidation"
            );
        }
        Ok(())
    }

    /// Helper for post-user-creation logic (DRY principle)
    async fn finalize_user_creation(
        &self,
        user: User,
        role_names_to_assign: Vec<String>,
    ) -> Result<UserWithRoles> {
        // 1. Assign roles
        if !role_names_to_assign.is_empty() {
            let roles = self
                .role_service
                .get_roles_by_names(&role_names_to_assign)
                .await?;
            if roles.len() != role_names_to_assign.len() {
                let found_names: std::collections::HashSet<String> =
                    roles.iter().map(|r| r.name.clone()).collect();
                for name in &role_names_to_assign {
                    if !found_names.contains(name) {
                        return Err(ErrorHelper::not_found("Role", Some(name)));
                    }
                }
            }
            let role_ids: Vec<Uuid> = roles.iter().map(|r| r.id).collect();
            self.user_role_repo
                .replace_roles_for_user(&user.id, &role_ids)
                .await?;
        }

        // 2. Initialize user level
        self.exp_system_service
            .on_new_user_created(&user.id)
            .await?;

        // 3. Return the final user object with roles
        Ok(UserWithRoles::from_user_and_roles(
            user,
            role_names_to_assign,
        ))
    }

    // Helper methods remain internal
    fn build_roles_map(roles_info: Vec<UserRoleInfo>) -> HashMap<Uuid, Vec<String>> {
        let mut roles_map: HashMap<Uuid, Vec<String>> = HashMap::new();
        for info in roles_info {
            roles_map
                .entry(info.user_id)
                .or_default()
                .push(info.role_name);
        }
        roles_map
    }

    // Helper to get role names for a user
    async fn get_user_role_names(&self, user_id: &Uuid) -> Result<Vec<String>> {
        self.user_role_repo.get_role_names_for_user(user_id).await
    }
}

// Implement UserQueryTrait
#[async_trait]
impl UserQueryTrait for UserService {
    async fn get_user_by_id(&self, id: &Uuid) -> Result<UserWithRoles> {
        let user = self
            .repo
            .find_by_id(id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("User", Some(&id.to_string())))?;

        let role_names = self.get_user_role_names(id).await?;

        Ok(UserWithRoles::from_user_and_roles(user, role_names))
    }

    async fn get_user_by_email(&self, email: &str) -> Result<User> {
        let user = self
            .repo
            .find_by_email(email)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_by_email("User", email))?;
        Ok(user)
    }

    async fn get_user_by_username(&self, username: &str) -> Result<User> {
        let user = self
            .repo
            .find_by_username(username)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_by_username("User", username))?;
        Ok(user)
    }

    async fn get_all_users(&self) -> Result<Vec<UserWithRoles>> {
        let users = self.repo.find_all().await?;
        let user_ids: Vec<Uuid> = users.iter().map(|u| u.id).collect();

        if user_ids.is_empty() {
            return Ok(vec![]);
        }

        let roles_info = self.user_role_repo.get_roles_for_users(&user_ids).await?;
        let roles_map = Self::build_roles_map(roles_info);

        let users_with_roles = users
            .into_iter()
            .map(|user| {
                let roles = roles_map.get(&user.id).cloned().unwrap_or_default();
                UserWithRoles::from_user_and_roles(user, roles)
            })
            .collect();

        Ok(users_with_roles)
    }

    async fn get_users_with_pagination(
        &self,
        pagination: UserPaginationRequest,
    ) -> Result<PaginatedUsersWithRoles> {
        pagination.validate_request()?;

        let paginated_result = self.repo.find_all_with_pagination(pagination).await?;
        let user_ids: Vec<Uuid> = paginated_result.data.iter().map(|u| u.id).collect();

        let roles_map = if !user_ids.is_empty() {
            let roles_info = self.user_role_repo.get_roles_for_users(&user_ids).await?;
            Self::build_roles_map(roles_info)
        } else {
            HashMap::new()
        };

        let users_with_roles = paginated_result
            .data
            .into_iter()
            .map(|user| {
                let roles = roles_map.get(&user.id).cloned().unwrap_or_default();
                UserWithRoles::from_user_and_roles(user, roles)
            })
            .collect();

        Ok(PaginatedUsersWithRoles {
            users: users_with_roles,
            pagination: paginated_result.pagination,
        })
    }

    async fn get_permission_version(&self, id: &Uuid) -> Result<Option<i32>> {
        if let Some(redis) = &self.redis_service {
            let cache_key = Self::permission_version_cache_key(id);
            if let Ok(Some(cached_str)) = redis.get(&cache_key).await {
                if let Ok(version) = cached_str.parse::<i32>() {
                    tracing::info!(
                        user_id = %id,
                        version = version,
                        cache_status = "hit",
                        "Permission version cache hit"
                    );
                    return Ok(Some(version));
                }
            }
            tracing::info!(
                user_id = %id,
                cache_status = "miss",
                "Permission version cache miss, falling back to database"
            );
        } else {
            tracing::debug!(
                user_id = %id,
                cache_status = "disabled",
                "Redis not available, querying database directly"
            );
        }

        let version = self.repo.get_permission_version(id).await?;
        if let Some(version) = version {
            if let Some(redis) = &self.redis_service {
                let cache_key = Self::permission_version_cache_key(id);
                let ttl = self.permission_version_ttl();
                if let Err(e) = redis.set(&cache_key, &version.to_string(), Some(ttl)).await {
                    tracing::warn!(
                        user_id = %id,
                        version = version,
                        ttl_secs = ttl.as_secs(),
                        error = %e,
                        "Failed to cache permission version"
                    );
                } else {
                    tracing::debug!(
                        user_id = %id,
                        version = version,
                        ttl_secs = ttl.as_secs(),
                        "Successfully cached permission version"
                    );
                }
            }
        }
        Ok(version)
    }
}

// Implement UserManagementTrait
#[async_trait]
impl UserManagementTrait for UserService {
    async fn create_user(&self, request: CreateUserRequest) -> Result<UserWithRoles> {
        request.validate_request()?;

        // Hash password using password service
        let password_hash = self
            .password_service
            .hash_password(&request.password)
            .await?;

        // Create user using repository method signature
        let created_user = self
            .repo
            .create(CreateUserParams {
                email: request.email.clone(),
                username: request.username.clone(),
                fullname: request.fullname.clone(),
                password_hash: Some(password_hash),
                oauth_provider: None,
                oauth_provider_id: None,
                avatar_url: None,
                email_verified: false,
            })
            .await?;

        // Determine roles and finalize creation
        let role_names_to_assign = request
            .role_names
            .unwrap_or_else(|| vec!["member".to_string()]);

        self.finalize_user_creation(created_user, role_names_to_assign)
            .await
    }

    async fn update_user(&self, id: &Uuid, request: UpdateUserRequest) -> Result<UserWithRoles> {
        request.validate_request()?;

        // Hash password if provided
        let hashed_password = if let Some(password) = request.password {
            Some(self.password_service.hash_password(&password).await?)
        } else {
            None
        };

        // Update user using repository method signature
        let updated_user = self
            .repo
            .update(
                id,
                request.email,
                request.username,
                request.fullname,
                hashed_password,
            )
            .await?;

        // Get current roles
        let role_names = self.get_user_role_names(id).await?;

        Ok(UserWithRoles::from_user_and_roles(updated_user, role_names))
    }

    async fn delete_user(&self, id: &Uuid) -> Result<()> {
        use crate::schema::{user_roles, users};
        use diesel::prelude::*;

        let user_id = *id;
        self.database
            .transaction::<_, _, AppError>(move |conn| {
                // 1. Delete all role assignments for the user.
                diesel::delete(user_roles::table.filter(user_roles::user_id.eq(user_id)))
                    .execute(conn)?;

                // 2. Delete the user record.
                let deleted_rows =
                    diesel::delete(users::table.filter(users::id.eq(&user_id))).execute(conn)?;

                if deleted_rows == 0 {
                    return Err(AppError::NotFound("User not found".into()));
                }

                Ok(())
            })
            .await
    }
}

// Implement UserRoleTrait
#[async_trait]
impl UserRoleTrait for UserService {
    async fn get_user_roles(&self, user_id: &Uuid) -> Result<Vec<Uuid>> {
        self.user_role_repo.get_user_roles(user_id).await
    }

    async fn user_has_role(&self, user_id: &Uuid, role_id: &Uuid) -> Result<bool> {
        self.user_role_repo.user_has_role(user_id, role_id).await
    }

    async fn assign_role_to_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()> {
        use crate::schema::{user_roles, users};
        use diesel::prelude::*;

        // Check if user and role exist before starting the transaction
        let _user = self
            .repo
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("User", Some(&user_id.to_string())))?;
        let _role = self.role_service.get_role_by_id(role_id).await?;

        let user_id = *user_id;
        let role_id = *role_id;

        self.database
            .transaction::<_, _, AppError>(move |conn| {
                // 1. Assign role, ignoring if it already exists
                diesel::insert_into(user_roles::table)
                    .values((
                        user_roles::user_id.eq(&user_id),
                        user_roles::role_id.eq(&role_id),
                    ))
                    .on_conflict_do_nothing()
                    .execute(conn)?;

                // 2. Increment permission version
                diesel::update(users::table.find(&user_id))
                    .set(users::permission_version.eq(users::permission_version + 1))
                    .execute(conn)?;

                Ok(())
            })
            .await?;

        // 3. Invalidate permission version cache after successful transaction
        self.invalidate_permission_version_cache(&user_id).await?;

        Ok(())
    }

    async fn remove_role_from_user(&self, user_id: &Uuid, role_id: &Uuid) -> Result<()> {
        use crate::schema::{user_roles, users};
        use diesel::prelude::*;

        let user_id = *user_id;
        let role_id = *role_id;

        self.database
            .transaction::<_, _, AppError>(move |conn| {
                // 1. Remove the role assignment
                diesel::delete(
                    user_roles::table
                        .filter(user_roles::user_id.eq(&user_id))
                        .filter(user_roles::role_id.eq(&role_id)),
                )
                .execute(conn)?;

                // 2. Increment permission version
                diesel::update(users::table.find(&user_id))
                    .set(users::permission_version.eq(users::permission_version + 1))
                    .execute(conn)?;

                Ok(())
            })
            .await?;

        // 3. Invalidate permission version cache after successful transaction
        self.invalidate_permission_version_cache(&user_id).await?;

        Ok(())
    }

    async fn update_user_roles(&self, user_id: &Uuid, role_ids: &[Uuid]) -> Result<UserWithRoles> {
        use crate::schema::{user_roles, users};
        use diesel::prelude::*;

        // Check if user and all roles exist before starting the transaction
        let user = self
            .repo
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("User", Some(&user_id.to_string())))?;
        if !role_ids.is_empty() {
            let found_roles = self.role_service.get_roles_by_ids(role_ids).await?;
            if found_roles.len() != role_ids.len() {
                let found_role_ids: std::collections::HashSet<Uuid> =
                    found_roles.iter().map(|r| r.id).collect();
                for role_id in role_ids {
                    if !found_role_ids.contains(role_id) {
                        return Err(ErrorHelper::not_found("Role", Some(&role_id.to_string())));
                    }
                }
            }
        }

        let user_id = *user_id;
        let role_ids = role_ids.to_vec();

        self.database
            .transaction::<_, _, AppError>(move |conn| {
                // 1. Delete all existing roles for the user
                diesel::delete(user_roles::table.filter(user_roles::user_id.eq(user_id)))
                    .execute(conn)?;

                // 2. Insert the new roles
                if !role_ids.is_empty() {
                    let new_assignments: Vec<_> = role_ids
                        .iter()
                        .map(|rid| (user_roles::user_id.eq(user_id), user_roles::role_id.eq(rid)))
                        .collect();

                    diesel::insert_into(user_roles::table)
                        .values(&new_assignments)
                        .execute(conn)?;
                }

                // 3. Increment permission version
                diesel::update(users::table.find(&user_id))
                    .set(users::permission_version.eq(users::permission_version + 1))
                    .execute(conn)?;

                Ok(())
            })
            .await?;

        // 4. Invalidate permission version cache after successful transaction
        self.invalidate_permission_version_cache(&user_id).await?;

        // Get updated role names and return the final state
        let role_names = self.get_user_role_names(&user_id).await?;
        Ok(UserWithRoles::from_user_and_roles(user, role_names))
    }
}

// Implement UserOAuthTrait
#[async_trait]
impl UserOAuthTrait for UserService {
    async fn create_oauth_user(&self, oauth_user: &OAuthUserInfo) -> Result<UserWithRoles> {
        // Generate unique username from user's email
        let base_username = oauth_user.email.split('@').next().unwrap_or("user");
        let mut username = base_username.to_string();
        let mut counter = 1;

        // Note: We still need to check username uniqueness because we generate it dynamically
        while self.repo.exists_by_username(&username).await? {
            username = format!("{}_{}", base_username, counter);
            counter += 1;
        }

        // Create OAuth user - let repository handle email duplicates via database constraints
        match self
            .repo
            .create(CreateUserParams {
                email: oauth_user.email.clone(),
                username,
                fullname: oauth_user.name.clone(),
                password_hash: None,                                 // No password for OAuth users
                oauth_provider: Some(oauth_user.provider.clone()),    // oauth_provider
                oauth_provider_id: Some(oauth_user.provider_id.clone()), // oauth_provider_id
                avatar_url: oauth_user.picture.clone(),           // avatar_url
                email_verified: oauth_user.verified_email,            // email_verified
            })
            .await
        {
            Ok(created_user) => {
                // Assign default member role
                let role_names_to_assign = vec!["member".to_string()];
                self.finalize_user_creation(created_user, role_names_to_assign)
                    .await
            }
            Err(AppError::Conflict(msg)) if msg.contains("email") => {
                // User already exists, this is expected in concurrent scenarios
                // Return the existing user instead
                let existing_user = self
                    .repo
                    .find_by_email(&oauth_user.email)
                    .await?
                    .ok_or_else(|| {
                        AppError::Internal(anyhow::anyhow!("User should exist but was not found"))
                    })?;

                // Get roles for existing user
                let role_names = self.get_user_role_names(&existing_user.id).await?;
                Ok(UserWithRoles::from_user_and_roles(
                    existing_user,
                    role_names,
                ))
            }
            Err(e) => Err(e),
        }
    }
}

// UserServiceTrait is automatically implemented through trait inheritance
impl UserServiceTrait for UserService {}

// Tests moved to test/permission_version_cache_test.rs
