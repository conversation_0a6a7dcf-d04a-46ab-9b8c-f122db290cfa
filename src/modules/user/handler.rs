use crate::{
    constants::API_USERS_PATH,
    errors::Result,
    modules::user::{
        models::{
            CreateUserRequest, PaginatedUsersWithRoles, UpdateUserRequest, UpdateUserRolesRequest,
            UserPaginationRequest, UserRolesResponse, UserWithRoles,
        },
        service_trait::UserServiceTrait,
    },
    response::{ApiResponse, ApiResponseJson},
    utils::response_helpers::{response_constants::user, ResponseHelper},
};
use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
    Json,
};
use serde::Deserialize;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Debug, Deserialize, utoipa::ToSchema)]
pub struct ReplaceUserRolesRequest {
    pub role_ids: Vec<Uuid>,
}

#[utoipa::path(
    post,
    path = "/api/users",
    tag = "Users",
    request_body = crate::modules::user::models::CreateUserRequest,
    responses(
        (status = 201, description = "User created successfully", body = ApiResponse<UserWithRoles>),
        (status = 400, description = "Invalid request data"),
        (status = 409, description = "User already exists")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn create_user(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
    Json(request): Json<CreateUserRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let user = user_service.create_user(request).await?;

    Ok(ResponseHelper::entity_created(
        API_USERS_PATH,
        user::SUCCESS_CREATE,
        user::MSG_CREATED,
        user,
    ))
}

#[utoipa::path(
    get,
    path = "/api/users/{id}",
    tag = "Users",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    responses(
        (status = 200, description = "User retrieved successfully", body = ApiResponse<UserWithRoles>),
        (status = 404, description = "User not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_user(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<axum::response::Response> {
    let path = format!("{}/{}", API_USERS_PATH, id);
    let user = crate::handle_service_result!(user_service.get_user_by_id(&id), path);

    Ok(ResponseHelper::entity_retrieved(
        API_USERS_PATH,
        Some(&id.to_string()),
        user::SUCCESS_GET,
        user::MSG_RETRIEVED,
        user,
    ).into_response())
}

pub async fn get_all_users(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
) -> Result<impl axum::response::IntoResponse> {
    let users = user_service.get_all_users().await?;

    let response_data = serde_json::json!({
        "users": users,
        "count": users.len()
    });

    Ok(ResponseHelper::entity_list(
        API_USERS_PATH,
        user::SUCCESS_LIST,
        user::MSG_LISTED,
        response_data,
        None,
    ))
}

#[utoipa::path(
    get,
    path = "/api/users",
    tag = "Users",
    params(
        ("page" = Option<i64>, Query, description = "Page number (default: 1)"),
        ("limit" = Option<i64>, Query, description = "Items per page (default: 10)")
    ),
    responses(
        (status = 200, description = "Users retrieved successfully", body = ApiResponse<PaginatedUsersWithRoles>),
        (status = 400, description = "Invalid pagination parameters")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_users_with_pagination(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
    Query(pagination): Query<UserPaginationRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let paginated_users = user_service.get_users_with_pagination(pagination).await?;

    let query_string = format!(
        "page={}&limit={}",
        paginated_users.pagination.page, paginated_users.pagination.limit
    );
    let query_params =
        if paginated_users.pagination.page == 1 && paginated_users.pagination.limit == 10 {
            None
        } else {
            Some(query_string.as_str())
        };

    Ok(ResponseHelper::entity_list(
        API_USERS_PATH,
        user::SUCCESS_LIST,
        user::MSG_LISTED,
        paginated_users,
        query_params,
    ))
}

#[utoipa::path(
    put,
    path = "/api/users/{id}",
    tag = "Users",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    request_body = crate::modules::user::models::UpdateUserRequest,
    responses(
        (status = 200, description = "User updated successfully", body = ApiResponse<UserWithRoles>),
        (status = 400, description = "Invalid request data"),
        (status = 404, description = "User not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_user(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdateUserRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let user = user_service.update_user(&id, request).await?;

    Ok(ResponseHelper::entity_retrieved(
        API_USERS_PATH,
        Some(&id.to_string()),
        user::SUCCESS_UPDATE,
        user::MSG_UPDATED,
        user,
    ))
}

#[utoipa::path(
    delete,
    path = "/api/users/{id}",
    tag = "Users",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    responses(
        (status = 200, description = "User deleted successfully", body = ApiResponseJson),
        (status = 404, description = "User not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn delete_user(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    user_service.delete_user(&id).await?;

    Ok(ResponseHelper::entity_deleted(
        API_USERS_PATH,
        &id.to_string(),
        user::SUCCESS_DELETE,
        user::MSG_DELETED,
    ))
}

#[utoipa::path(
    get,
    path = "/api/users/{id}/roles",
    tag = "Users",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    responses(
        (status = 200, description = "User roles retrieved successfully", body = ApiResponse<UserRolesResponse>),
        (status = 404, description = "User not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn get_user_roles(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
    Path(id): Path<Uuid>,
) -> Result<impl axum::response::IntoResponse> {
    let user_with_roles = user_service.get_user_by_id(&id).await?;

    let response_data = UserRolesResponse {
        roles: user_with_roles.roles,
    };

    Ok(crate::response::success_response(
        format!("/api/users/{}/roles", id),
        crate::utils::response_helpers::response_constants::user::SUCCESS_GET,
        "User roles retrieved successfully",
        response_data,
    ))
}

#[utoipa::path(
    put,
    path = "/api/users/{id}/roles",
    tag = "Users",
    params(
        ("id" = String, Path, description = "User ID")
    ),
    request_body = UpdateUserRolesRequest,
    responses(
        (status = 200, description = "User roles updated successfully", body = ApiResponse<UserWithRoles>),
        (status = 400, description = "Invalid role data"),
        (status = 404, description = "User not found")
    ),
    security(
        ("bearer_auth" = [])
    )
)]
pub async fn update_user_roles(
    State(user_service): State<Arc<dyn UserServiceTrait>>,
    Path(id): Path<Uuid>,
    Json(request): Json<UpdateUserRolesRequest>,
) -> Result<impl axum::response::IntoResponse> {
    let updated_user = user_service
        .update_user_roles(&id, &request.role_ids)
        .await?;

    Ok(ResponseHelper::entity_retrieved(
        API_USERS_PATH,
        Some(&id.to_string()),
        user::SUCCESS_UPDATE,
        "User roles updated successfully",
        updated_user,
    ))
}
