use crate::schema::categories;
use crate::utils::pagination::{PaginationMeta, PaginationRequest};
use crate::utils::validation::{ValidateRequestEnhanced, ValidationResult};
use crate::utils::{validate_required_string, validate_string_length};
use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

// ===== DOMAIN MODELS =====

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops",
    "is_active": true,
    "created_at": "2025-07-12T01:31:59.286900Z",
    "updated_at": "2025-07-12T01:31:59.286900Z"
}))]
pub struct Category {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops",
    "is_active": true,
    "created_by": "15ef5806-6578-46ca-b999-89c391079e7a",
    "updated_by": "15ef5806-6578-46ca-b999-89c391079e7a",
    "created_at": "2025-07-12T01:31:59.286900Z",
    "updated_at": "2025-07-12T01:31:59.286900Z"
}))]
pub struct CategoryWithTracking {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: String,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ===== REQUEST MODELS =====

#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "name": "Gaming Laptops",
    "slug": "gaming-laptops",
    "description": "High-performance laptops for gaming",
    "category_type": "laptops"
}))]
pub struct CreateCategoryRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Category name must be between 2 and 50 characters"
    ))]
    pub name: String,

    #[validate(length(
        min = 2,
        max = 60,
        message = "Category slug must be between 2 and 60 characters"
    ))]
    pub slug: String,

    #[validate(length(max = 500, message = "Description must not exceed 500 characters"))]
    pub description: Option<String>,

    #[validate(length(
        min = 2,
        max = 20,
        message = "Category type must be between 2 and 20 characters"
    ))]
    pub category_type: String,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "name": "Updated Gaming Laptops",
    "description": "Updated description for gaming laptops",
    "is_active": false
}))]
pub struct UpdateCategoryRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Category name must be between 2 and 50 characters"
    ))]
    pub name: Option<String>,

    #[validate(length(max = 500, message = "Description must not exceed 500 characters"))]
    pub description: Option<Option<String>>,

    pub is_active: Option<bool>,
}

// ===== PAGINATION MODELS =====



#[derive(Debug, Clone, Deserialize, Serialize, ToSchema, IntoParams)]
pub struct CategoryPaginationRequest {
    #[serde(default = "default_page")]
    pub page: i64,
    #[serde(default = "default_limit")]
    pub limit: i64,

    /// Filter by category type
    pub category_type: Option<String>,

    /// Filter by active status
    pub is_active: Option<bool>,

    /// Search by name or description
    pub search: Option<String>,
}

impl PaginationRequest for CategoryPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }

    fn limit(&self) -> i64 {
        self.limit
    }
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "categories": [
        {
            "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "name": "Gaming Laptops",
            "slug": "gaming-laptops",
            "description": "High-performance laptops for gaming",
            "category_type": "laptops",
            "is_active": true,
            "created_at": "2025-07-12T01:31:59.286900Z",
            "updated_at": "2025-07-12T01:31:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 1,
        "total_pages": 1
    }
}))]
pub struct PaginatedCategories {
    pub categories: Vec<Category>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedCategoriesWithTracking {
    pub categories: Vec<CategoryWithTracking>,
    pub meta: PaginationMeta,
}

// ===== VALIDATION IMPLEMENTATION =====

impl ValidateRequestEnhanced for CreateCategoryRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        // Validate name
        validate_required_string(&self.name, "name", &mut result);

        // Validate slug
        validate_required_string(&self.slug, "slug", &mut result);

        // Validate slug format (alphanumeric, hyphens, underscores only)
        if !self.slug.chars().all(|c| c.is_alphanumeric() || c == '-' || c == '_') {
            result.add_error("slug", "INVALID_FORMAT", "Slug can only contain alphanumeric characters, hyphens, and underscores");
        }

        // Validate category_type
        validate_required_string(&self.category_type, "category_type", &mut result);

        // Validate description if provided
        if let Some(ref desc) = self.description {
            validate_string_length(desc, "description", 0, 500, &mut result);
        }

        result
    }
}

impl ValidateRequestEnhanced for UpdateCategoryRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut result = ValidationResult::new();

        // Validate name if provided
        if let Some(ref name) = self.name {
            validate_required_string(name, "name", &mut result);
        }

        // Validate description if provided
        if let Some(ref desc_opt) = self.description {
            if let Some(ref desc) = desc_opt {
                validate_string_length(desc, "description", 0, 500, &mut result);
            }
        }

        result
    }
}

// ===== DIESEL DATABASE MODELS =====

#[derive(Debug, Clone, Queryable, Selectable, Serialize, Deserialize)]
#[diesel(table_name = categories)]
pub struct DieselCategory {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: String,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Insertable)]
#[diesel(table_name = categories)]
pub struct NewCategory {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: String,
    pub is_active: bool,
    pub created_by: Option<Uuid>,
}

#[derive(Debug, Clone, AsChangeset)]
#[diesel(table_name = categories)]
pub struct UpdateCategory {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub is_active: Option<bool>,
    pub updated_by: Option<Uuid>,
    pub updated_at: DateTime<Utc>,
}

// ===== CONVERSIONS =====

impl From<DieselCategory> for Category {
    fn from(diesel_category: DieselCategory) -> Self {
        Self {
            id: diesel_category.id,
            name: diesel_category.name,
            slug: diesel_category.slug,
            description: diesel_category.description,
            category_type: diesel_category.category_type,
            is_active: diesel_category.is_active,
            created_at: diesel_category.created_at,
            updated_at: diesel_category.updated_at,
        }
    }
}

impl From<DieselCategory> for CategoryWithTracking {
    fn from(diesel_category: DieselCategory) -> Self {
        Self {
            id: diesel_category.id,
            name: diesel_category.name,
            slug: diesel_category.slug,
            description: diesel_category.description,
            category_type: diesel_category.category_type,
            is_active: diesel_category.is_active,
            created_by: diesel_category.created_by,
            updated_by: diesel_category.updated_by,
            created_at: diesel_category.created_at,
            updated_at: diesel_category.updated_at,
        }
    }
}

// ===== HELPER FUNCTIONS =====

fn default_page() -> i64 {
    1
}

fn default_limit() -> i64 {
    10
}
