use platform_rust::modules::laptop::models::LaptopStatus;
use uuid::Uuid;

#[tokio::test]
async fn test_laptop_models_serialization() {
    use platform_rust::modules::laptop::models::*;
    use serde_json;

    // Test LaptopStatus serialization
    let status = LaptopStatus::Published;
    let serialized = serde_json::to_string(&status).unwrap();
    assert_eq!(serialized, "\"published\"");

    // Test ScreenResolution serialization
    let resolution = ScreenResolution::FourK;
    let serialized = serde_json::to_string(&resolution).unwrap();
    assert_eq!(serialized, "\"4K\"");

    // Test GpuType serialization
    let gpu_type = GpuType::Dedicated;
    let serialized = serde_json::to_string(&gpu_type).unwrap();
    assert_eq!(serialized, "\"Dedicated\"");

    // Test StorageType serialization
    let storage_type = StorageType::SSD;
    let serialized = serde_json::to_string(&storage_type).unwrap();
    assert_eq!(serialized, "\"SSD\"");

    // Test RamType serialization
    let ram_type = RamType::DDR5;
    let serialized = serde_json::to_string(&ram_type).unwrap();
    assert_eq!(serialized, "\"DDR5\"");
}

#[tokio::test]
async fn test_create_laptop_request_validation() {
    use platform_rust::modules::laptop::models::CreateLaptopRequest;
    use validator::Validate;

    // Test valid request
    let valid_request = CreateLaptopRequest {
        brand: "Apple".to_string(),
        model: "MacBook Pro".to_string(),
        full_name: "Apple MacBook Pro 14-inch M3 Pro".to_string(),
        slug: "apple-macbook-pro-14-m3-pro".to_string(),
        category_id: Uuid::new_v4(),
        sku: Some("MBP-14-M3-PRO".to_string()),
        market_region: Some("Global".to_string()),
        release_date: None,
        description: Some("Latest MacBook Pro with M3 Pro chip".to_string()),
        image_urls: Some(vec!["https://example.com/image1.jpg".to_string()]),
        is_featured: Some(false),
    };

    assert!(valid_request.validate().is_ok());

    // Test invalid request (empty brand)
    let invalid_request = CreateLaptopRequest {
        brand: "".to_string(),
        model: "MacBook Pro".to_string(),
        full_name: "Apple MacBook Pro 14-inch M3 Pro".to_string(),
        slug: "apple-macbook-pro-14-m3-pro".to_string(),
        category_id: Uuid::new_v4(),
        sku: Some("MBP-14-M3-PRO".to_string()),
        market_region: Some("Global".to_string()),
        release_date: None,
        description: Some("Latest MacBook Pro with M3 Pro chip".to_string()),
        image_urls: Some(vec!["https://example.com/image1.jpg".to_string()]),
        is_featured: Some(false),
    };

    assert!(invalid_request.validate().is_err());
}

#[tokio::test]
async fn test_pagination_request_validation() {
    use platform_rust::modules::laptop::models::LaptopPaginationRequest;
    use validator::Validate;

    // Test valid pagination request
    let valid_request = LaptopPaginationRequest {
        page: Some(1),
        per_page: Some(10),
        search: Some("MacBook".to_string()),
        brand: Some("Apple".to_string()),
        category_id: Some(Uuid::new_v4()),
        status: Some(LaptopStatus::Published),
        is_featured: Some(true),
        market_region: Some("Global".to_string()),
    };

    assert!(valid_request.validate().is_ok());

    // Test invalid pagination request (page = 0)
    let invalid_request = LaptopPaginationRequest {
        page: Some(0),
        per_page: Some(10),
        search: None,
        brand: None,
        category_id: None,
        status: None,
        is_featured: None,
        market_region: None,
    };

    assert!(invalid_request.validate().is_err());

    // Test invalid pagination request (per_page > 100)
    let invalid_request2 = LaptopPaginationRequest {
        page: Some(1),
        per_page: Some(101),
        search: None,
        brand: None,
        category_id: None,
        status: None,
        is_featured: None,
        market_region: None,
    };

    assert!(invalid_request2.validate().is_err());
}

#[tokio::test]
async fn test_model_conversions() {
    use platform_rust::modules::laptop::models::*;
    use chrono::Utc;

    // Create a DieselLaptop for testing conversions
    let diesel_laptop = DieselLaptop {
        id: Uuid::new_v4(),
        category_id: Uuid::new_v4(),
        brand: "Apple".to_string(),
        model: "MacBook Pro".to_string(),
        full_name: "Apple MacBook Pro 14-inch M3 Pro".to_string(),
        slug: "apple-macbook-pro-14-m3-pro".to_string(),
        sku: Some("MBP-14-M3-PRO".to_string()),
        market_region: "Global".to_string(),
        release_date: None,
        description: Some("Latest MacBook Pro with M3 Pro chip".to_string()),
        image_urls: Some(vec![Some("https://example.com/image1.jpg".to_string())]),
        status: "published".to_string(),
        is_featured: false,
        view_count: 0,
        created_by: Uuid::new_v4(),
        updated_by: None,
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    // Test conversion to LaptopPublicView
    let public_view = LaptopPublicView::from(diesel_laptop.clone());
    assert_eq!(public_view.brand, "Apple");
    assert_eq!(public_view.status, "published");

    // Test conversion to LaptopDetailedView
    let detailed_view = LaptopDetailedView::from(diesel_laptop.clone());
    assert_eq!(detailed_view.brand, "Apple");
    assert_eq!(detailed_view.status, "published");
    assert!(detailed_view.created_at <= Utc::now());

    // Test conversion to LaptopFullView
    let full_view = LaptopFullView::from(diesel_laptop);
    assert_eq!(full_view.brand, "Apple");
    assert_eq!(full_view.status, "published");
    assert!(full_view.created_by != Uuid::nil());
}
